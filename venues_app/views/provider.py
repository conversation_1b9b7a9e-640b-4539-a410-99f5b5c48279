"""Views for service provider venue and service management."""

# --- Standard Library Imports ---
from datetime import timedelta
import json
import logging
from collections import defaultdict
from django.utils import timezone
import decimal

# --- Django Imports ---
from django.contrib import messages
from django.contrib.auth.decorators import login_required, user_passes_test
from django.contrib.auth.mixins import LoginRequiredMixin
from django.core.paginator import Paginator
from django.db.models import Avg, Max
from django.db import transaction
from django.shortcuts import get_object_or_404, redirect, render
from django.urls import reverse_lazy
from django.views.generic import CreateView, DeleteView, UpdateView
from django.http import JsonResponse, HttpResponseForbidden
from django.views.decorators.http import require_POST
from django.core.exceptions import ValidationError, PermissionDenied
from django.core.cache import cache
from django.utils import timezone
from django.views.decorators.csrf import csrf_exempt
from django.core.files.storage import default_storage
from django.core.exceptions import ValidationError, PermissionDenied
from django.core.paginator import Paginator
from django.db import transaction, IntegrityError
from django.db.models import Q, Count, Avg, Max, Prefetch
from django.http import JsonResponse, HttpResponseRedirect, Http404, HttpResponseForbidden
from django.urls import reverse, reverse_lazy
from django.utils.decorators import method_decorator
from django.utils.text import slugify
from django.utils.translation import gettext_lazy as _
from django.views import View
from django.views.decorators.http import require_POST, require_http_methods
from django.views.generic import CreateView, UpdateView, DeleteView

# --- Local App Imports ---
from booking_cart_app.models import Booking
from ..forms import (
    VenueFAQForm, ServiceForm, VenueForm, OperatingHoursForm,
    OperatingHoursFormSetFactory, VenueAmenityForm,
    VenueImageForm, VenueGalleryImagesForm, VenueWithOperatingHoursForm,
    SimplifiedOperatingHoursForm, HolidayScheduleForm, VenueVisibilityForm,
    VenueBasicInformationEditForm, VenueLocationEditForm
)
from ..forms.venue import VenueCreateForm
from ..models import (
    Venue, Category, VenueImage, VenueFAQ, VenueAmenity, 
    OperatingHours, HolidaySchedule, USCity, Service, VenueCreationDraft
)
from .common import MAX_FAQS_PER_VENUE, MAX_SERVICES_PER_VENUE, ServiceProviderRequiredMixin
from ..utils import (
    validate_location_combination, 
    find_matching_uscity, 
    get_location_suggestions_with_fuzzy_matching,
    validate_phone_number,
    validate_business_email,
    validate_website_url,
    handle_venue_creation_error,
    ValidationMessages,
    ErrorHandler
)

# --- Additional App Imports ---
from accounts_app.models import ServiceProviderProfile
from ..forms import (
    VenueForm, VenueFAQForm, VenueAmenityForm, VenueImageForm,
    OperatingHoursForm, HolidayScheduleForm
)
from ..forms.venue import VenueCreateForm, VenueWithOperatingHoursForm
from ..forms.operating_hours import SimplifiedOperatingHoursForm
from ..forms.service import ServiceForm
from ..utils import (
    get_approval_progress, get_location_suggestions, requires_reapproval,
    handle_venue_creation_error, validate_phone_number, validate_business_email, 
    validate_website_url, sync_contact_from_service_provider,
    generate_email_verification_token
)


class VenueCreationWizardView:
    """Multi-step venue creation wizard with database-backed progress saving"""
    
    def __init__(self, request, user):
        self.request = request
        self.user = user
        self.service_provider = user.service_provider_profile
    
    def get_or_create_draft(self):
        """Get or create venue creation draft"""
        draft, created = VenueCreationDraft.objects.get_or_create(
            service_provider=self.service_provider,
            defaults={'current_step': 'basic'}
        )
        return draft, created
    
    def save_progress(self, step, form_data):
        """Save current progress to database"""
        draft, created = self.get_or_create_draft()
        
        # Update draft with current form data
        draft.update_from_form_data(form_data, step)
        
        # Also keep cache for backward compatibility (optional)
        cache_key = f'venue_creation_wizard_{self.user.id}'
        progress_data = cache.get(cache_key, {})
        progress_data[step] = form_data
        progress_data['last_step'] = step
        progress_data['progress_percentage'] = draft.get_progress_percentage()
        cache.set(cache_key, progress_data, timeout=3600 * 24)  # 24 hours
        
        return {
            'draft_id': draft.id,
            'progress_percentage': draft.get_progress_percentage(),
            'current_step': draft.current_step,
            'completed_steps': draft.completed_steps,
            'last_updated': draft.updated_at.isoformat() if draft.updated_at else None,
        }
    
    def get_progress(self):
        """Get saved progress from database"""
        try:
            draft = VenueCreationDraft.objects.get(service_provider=self.service_provider)
            return draft.to_dict()
        except VenueCreationDraft.DoesNotExist:
            return {}
    
    def get_draft(self):
        """Get the draft object if it exists"""
        try:
            return VenueCreationDraft.objects.get(service_provider=self.service_provider)
        except VenueCreationDraft.DoesNotExist:
            return None
    
    def clear_progress(self):
        """Clear saved progress from database and cache"""
        try:
            draft = VenueCreationDraft.objects.get(service_provider=self.service_provider)
            draft.delete()
        except VenueCreationDraft.DoesNotExist:
            pass
        
        # Also clear cache
        cache_key = f'venue_creation_wizard_{self.user.id}'
        cache.delete(cache_key)
    
    def auto_save_progress(self, form_data, step):
        """Auto-save progress without validation - for real-time saving"""
        try:
            draft, created = self.get_or_create_draft()
            
            # Clean form data - remove CSRF token and action
            clean_data = {k: v for k, v in form_data.items() 
                         if k not in ['csrfmiddlewaretoken', 'action']}
            
            draft.update_from_form_data(clean_data, step)
            
            return {
                'success': True,
                'draft_id': draft.id,
                'progress_percentage': draft.get_progress_percentage(),
                'last_updated': draft.updated_at.isoformat(),
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def _calculate_progress(self, progress_data):
        """Calculate overall completion percentage - updated for 5-step wizard"""
        try:
            draft = VenueCreationDraft.objects.get(service_provider=self.service_provider)
            return draft.get_progress_percentage()
        except VenueCreationDraft.DoesNotExist:
            # Fallback to old calculation
            required_steps = ['basic', 'location', 'services', 'gallery', 'details']
            completed_steps = sum(1 for step in required_steps if step in progress_data)
            return min(int((completed_steps / len(required_steps)) * 100), 100)

    def get_next_step(self, current_step):
        """Get next step in the 5-step wizard"""
        steps = ['basic', 'location', 'services', 'gallery', 'details']
        try:
            current_index = steps.index(current_step)
            return steps[current_index + 1] if current_index < len(steps) - 1 else 'details'
        except ValueError:
            return 'basic'

    def get_previous_step(self, current_step):
        """Get previous step in the 5-step wizard"""
        steps = ['basic', 'location', 'services', 'gallery', 'details']
        try:
            current_index = steps.index(current_step)
            return steps[current_index - 1] if current_index > 0 else None
        except ValueError:
            return None

    def get_step_form_class(self, step):
        """Get the appropriate form class for each step"""
        from ..forms.venue import (
            VenueBasicInfoForm, VenueLocationContactForm, VenueServicesForm,
            VenueGalleryOnlyForm, VenueDetailsForm
        )

        form_mapping = {
            'basic': VenueBasicInfoForm,
            'location': VenueLocationContactForm,
            'services': VenueServicesForm,
            'gallery': VenueGalleryOnlyForm,
            'details': VenueDetailsForm,
        }

        return form_mapping.get(step, VenueBasicInfoForm)

    def validate_step_data(self, step, form_data):
        """Validate data for a specific step"""
        form_class = self.get_step_form_class(step)
        form = form_class(data=form_data)

        if form.is_valid():
            return True, {}
        else:
            return False, form.errors

    def handle_complex_data(self, step, form_data):
        """Handle complex form data like operating hours, services, etc."""
        if step == 'services':
            # Handle services data
            pass
        elif step == 'gallery':
            # Handle image uploads - convert uploaded files to proper format
            if 'images' in form_data and form_data['images']:
                try:
                    # If images is already JSON string, parse it
                    if isinstance(form_data['images'], str):
                        images_data = json.loads(form_data['images'])
                    else:
                        images_data = form_data['images']
                    
                    # Ensure images_data is a list
                    if not isinstance(images_data, list):
                        images_data = []
                    
                    # Filter out any invalid entries and ensure proper structure
                    valid_images = []
                    for img in images_data:
                        if isinstance(img, dict) and 'url' in img:
                            valid_images.append({
                                'url': img.get('url'),
                                'name': img.get('name', ''),
                                'order': img.get('order', len(valid_images) + 1),
                                'is_main': img.get('is_main', False)
                            })
                    
                    form_data['images'] = valid_images
                except (json.JSONDecodeError, TypeError):
                    # If parsing fails, initialize empty list
                    form_data['images'] = []
        elif step == 'details':
            # Handle operating hours, amenities, FAQs
            pass
        
        return form_data

    def get_step_requirements(self, step):
        """Get requirements for each step"""
        requirements = {
            'basic': {
                'required_fields': ['venue_name', 'short_description', 'categories'],
                'description': 'Complete basic venue information',
                'details': [
                    'Enter a unique venue name (3-100 characters)',
                    'Write a compelling description (50-500 characters)', 
                    'Select 1-3 relevant categories'
                ]
            },
            'location': {
                'required_fields': ['street_address', 'city', 'state', 'zip_code', 'phone'],
                'description': 'Provide accurate location and contact details',
                'details': [
                    'Enter complete street address',
                    'Verify city, state, and ZIP code',
                    'Add business phone number',
                    'Optional: Add email and website'
                ]
            },
            'services': {
                'required_fields': ['services'],
                'description': 'Add your services with pricing',
                'details': [
                    'Add at least 1 service',
                    'Include clear service descriptions',
                    'Set appropriate pricing'
                ]
            },
            'gallery': {
                'required_fields': ['images'],
                'description': 'Upload venue photos',
                'details': [
                    'Upload at least 1 main image',
                    'Add 2-5 gallery images (recommended)',
                    'Use high-quality, well-lit photos'
                ]
            },
            'details': {
                'required_fields': ['operating_hours'],
                'description': 'Complete venue details and policies',
                'details': [
                    'Set operating hours for each day',
                    'Add venue amenities',
                    'Optional: Add FAQs and policies'
                ]
            }
        }
        
        return requirements.get(step, {})

    def check_step_completion(self, step, form_data):
        """Check if a step meets completion requirements"""
        requirements = self.get_step_requirements(step)
        required_fields = requirements.get('required_fields', [])
        
        missing_fields = []
        completion_score = 0
        total_score = len(required_fields)
        
        for field in required_fields:
            if field in form_data and form_data[field]:
                # Special handling for complex fields
                if field == 'categories':
                    if isinstance(form_data[field], list) and len(form_data[field]) > 0:
                        completion_score += 1
                    else:
                        missing_fields.append(field)
                elif field == 'services':
                    # Check if services data exists and is valid
                    if form_data.get('services') and len(form_data['services']) > 0:
                        completion_score += 1
                    else:
                        missing_fields.append(field)
                elif field == 'images':
                    # Check if images data exists
                    if form_data.get('images') or form_data.get('main_image'):
                        completion_score += 1
                    else:
                        missing_fields.append(field)
                else:
                    # Standard field check
                    if str(form_data[field]).strip():
                        completion_score += 1
                    else:
                        missing_fields.append(field)
            else:
                missing_fields.append(field)
        
        completion_percentage = int((completion_score / total_score) * 100) if total_score > 0 else 0
        
        return {
            'complete': completion_percentage >= 80,  # 80% threshold
            'percentage': completion_percentage,
            'missing_fields': missing_fields,
            'completed_fields': completion_score,
            'total_fields': total_score
        }

    def get_step_completion_status(self):
        """Get completion status for all steps"""
        try:
            draft = VenueCreationDraft.objects.get(service_provider=self.service_provider)
            progress_data = draft.to_dict()
            
            steps = ['basic', 'location', 'services', 'gallery', 'details']
            step_status = {}
            
            for step in steps:
                step_data = {}
                
                # Extract step-specific data from progress
                if step == 'basic':
                    step_data = {
                        'venue_name': progress_data.get('venue_name', ''),
                        'short_description': progress_data.get('short_description', ''),
                        'categories': progress_data.get('categories', [])
                    }
                elif step == 'location':
                    step_data = {
                        'street_address': progress_data.get('street_address', ''),
                        'city': progress_data.get('city', ''),
                        'state': progress_data.get('state', ''),
                        'zip_code': progress_data.get('zip_code', ''),
                        'phone': progress_data.get('phone', ''),
                        'email': progress_data.get('email', ''),
                        'website_url': progress_data.get('website_url', '')
                    }
                elif step == 'services':
                    step_data = {
                        'services': progress_data.get('services', [])
                    }
                elif step == 'gallery':
                    step_data = {
                        'images': progress_data.get('images', []),
                        'main_image': progress_data.get('main_image', '')
                    }
                elif step == 'details':
                    step_data = {
                        'operating_hours': progress_data.get('operating_hours', {}),
                        'amenities': progress_data.get('amenities', []),
                        'faqs': progress_data.get('faqs', [])
                    }
                
                completion = self.check_step_completion(step, step_data)
                step_status[step] = completion
            
            return step_status
            
        except VenueCreationDraft.DoesNotExist:
            # Return empty status if no draft exists
            steps = ['basic', 'location', 'services', 'gallery', 'details']
            return {step: {'complete': False, 'percentage': 0, 'missing_fields': [], 'completed_fields': 0, 'total_fields': 0} for step in steps}


@login_required
def venue_create_wizard_view(request, step='basic'):
    """Multi-step venue creation wizard with progress saving and guided tour."""
    # Ensure user has service provider profile
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'You must have a service provider profile to create a venue.')
        return redirect('accounts_app:service_provider_profile')

    # Check if user already has a venue
    existing_venue = Venue.objects.filter(
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    ).first()

    if existing_venue:
        messages.info(request, 'You already have a venue. You can only have one venue per account. You can edit your existing venue or manage its details below.')
        return redirect('venues_app:venue_edit')

    # Initialize wizard
    wizard = VenueCreationWizardView(request, request.user)
    
    # Initialize variables for all code paths to prevent UnboundLocalError
    progress_data = wizard.get_progress()
    draft = wizard.get_draft()
    
    # Handle AJAX requests for progress saving
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return handle_wizard_ajax(request, wizard, step)

    if request.method == 'POST':
        # Handle form submission - merge current POST data with saved progress
        merged_data = progress_data.copy()

        # Convert POST data to dict, handling multiple values correctly for checkboxes
        post_data = {}
        for key in request.POST.keys():
            if key != 'csrfmiddlewaretoken':
                # Handle multiple values for fields like categories (checkboxes)
                values = request.POST.getlist(key)
                if len(values) == 1:
                    post_data[key] = values[0]
                else:
                    post_data[key] = values

        # Special handling for categories field - convert string IDs to integers
        if 'categories' in post_data and isinstance(post_data['categories'], list):
            try:
                post_data['categories'] = [int(cat_id) for cat_id in post_data['categories'] if cat_id.strip()]
            except (ValueError, TypeError):
                # If conversion fails, keep the original data and let form validation handle the error
                pass

        merged_data.update(post_data)

        # Get the appropriate form class for the current step
        form_class = wizard.get_step_form_class(step)
        form = form_class(data=merged_data, current_step=step)

        if form.is_valid():
            # Handle complex data processing
            processed_data = wizard.handle_complex_data(step, form.cleaned_data)

            # If this is the final step, create the venue
            if step == 'details':
                return create_venue_from_wizard(request, wizard, form)
            else:
                # Save progress and move to next step
                progress_result = wizard.save_progress(step, processed_data)
                next_step = wizard.get_next_step(step)

                # Get step titles for the new 5-step structure
                step_titles = {
                    'basic': 'Basic Information',
                    'location': 'Location',
                    'services': 'Services',
                    'gallery': 'Gallery',
                    'details': 'Details'
                }

                messages.success(
                    request,
                    f'Progress saved! Moving to {step_titles.get(next_step, next_step.title())} step.'
                )

                return redirect('venues_app:venue_create_wizard', step=next_step)
        else:
            # Show validation errors
            for field, errors in form.errors.items():
                for error in errors:
                    # Fix: Properly handle getting field labels
                    if field in form.fields:
                        field_label = form.fields[field].label or field.replace('_', ' ').title()
                    else:
                        field_label = field.replace('_', ' ').title()
                    messages.error(request, f"{field_label}: {error}")
    else:
        # Show message if restoring from draft
        if draft and progress_data:
            time_since_update = timezone.now() - draft.updated_at
            if time_since_update.days > 0:
                time_str = f"{time_since_update.days} day(s) ago"
            elif time_since_update.seconds > 3600:
                hours = time_since_update.seconds // 3600
                time_str = f"{hours} hour(s) ago"
            else:
                minutes = time_since_update.seconds // 60
                time_str = f"{minutes} minute(s) ago"
            
            messages.info(
                request,
                f'✅ Welcome back! We found your saved progress from {time_str}. You can continue where you left off or start fresh.'
            )
        
        # Initialize form with progress data using the appropriate form class
        form_class = wizard.get_step_form_class(step)
        form = form_class(initial=progress_data, current_step=step)

    # Ensure form is initialized if not set in POST error case
    if 'form' not in locals():
        form_class = wizard.get_step_form_class(step)
        form = form_class(initial=progress_data, current_step=step)

    # Prepare context for 5-step wizard
    from ..forms.venue import VenueCreateWizardMixin
    step_choices = VenueCreateWizardMixin.STEP_CHOICES

    step_order = ['basic', 'location', 'services', 'gallery', 'details']

    # Get completed steps from draft for proper progress indication
    completed_steps = []
    if draft:
        # Only include steps that are in the new 5-step structure
        valid_steps = ['basic', 'location', 'services', 'gallery', 'details']
        completed_steps = [step for step in draft.completed_steps if step in valid_steps]

    context = {
        'form': form,
        'current_step': step,
        'step_choices': step_choices,
        'current_step_title': dict(step_choices).get(step, step.title()),
        'progress_data': progress_data,
        'completed_steps': completed_steps,
        'progress_percentage': wizard._calculate_progress(progress_data),
        'next_step': wizard.get_next_step(step),
        'previous_step': wizard.get_previous_step(step),
        'is_first_step': step == 'basic',
        'is_final_step': step == 'details',
        'step_number': step_order.index(step) + 1 if step in step_order else 1,
        'total_steps': 5,
        'has_draft': draft is not None,
        'draft_updated_at': draft.updated_at if draft else None,
    }
    
    # Add guided tour data for first-time users
    if not request.user.service_provider_profile.venue_creation_tutorial_completed:
        context['show_guided_tour'] = True
        context['tour_steps'] = get_guided_tour_steps(step)
    
    return render(request, 'venues_app/venue_create_wizard.html', context)


def handle_wizard_ajax(request, wizard, step):
    """Handle AJAX requests for the wizard"""
    if request.method == 'POST':
        action = request.POST.get('action')
        
        if action == 'save_progress':
            # Save progress without validation for auto-save, handling multiple values
            form_data = {}
            for key in request.POST.keys():
                if key != 'csrfmiddlewaretoken':
                    values = request.POST.getlist(key)
                    if len(values) == 1:
                        form_data[key] = values[0]
                    else:
                        form_data[key] = values
            
            # Special handling for categories field in auto-save
            if 'categories' in form_data and isinstance(form_data['categories'], list):
                try:
                    form_data['categories'] = [int(cat_id) for cat_id in form_data['categories'] if cat_id.strip()]
                except (ValueError, TypeError):
                    # If conversion fails, keep the original data
                    pass
            
            result = wizard.auto_save_progress(form_data, step)
            
            if result['success']:
                # Get step completion status
                draft = wizard.get_draft()
                step_completion = wizard.get_step_completion_status() if draft else {}
                
                return JsonResponse({
                    'success': True,
                    'message': 'Progress saved automatically',
                    'progress_percentage': result.get('progress_percentage', 0),
                    'draft_id': result.get('draft_id'),
                    'last_updated': result.get('last_updated'),
                    'step_completion': step_completion,
                    'auto_save_status': 'saved'
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': f'Auto-save failed: {result.get("error", "Unknown error")}',
                    'auto_save_status': 'failed'
                })
        
        elif action == 'validate_field':
            # Real-time field validation using step-specific forms
            field_name = request.POST.get('field_name')
            field_value = request.POST.get('field_value')

            # Get the appropriate form class for the current step
            form_class = wizard.get_step_form_class(step)
            form = form_class(data={field_name: field_value}, current_step=step)

            validation_result = {'is_valid': True, 'errors': [], 'suggestions': []}

            if not form.is_valid() and field_name in form.errors:
                # Enhanced error messages with suggestions
                error_messages = []
                suggestions = []
                
                for error in form.errors[field_name]:
                    error_messages.append(str(error))
                    
                    # Add specific suggestions based on field and error
                    if field_name == 'venue_name':
                        if 'required' in str(error).lower():
                            suggestions.append("Choose a memorable name that describes your venue")
                        elif 'length' in str(error).lower():
                            suggestions.append("Keep the name between 3-100 characters")
                    elif field_name == 'phone':
                        suggestions.append("Include area code (e.g., (*************)")
                    elif field_name == 'email':
                        suggestions.append("Use a valid business email address")
                    elif field_name == 'website_url':
                        suggestions.append("Include http:// or https:// (e.g., https://example.com)")
                    elif field_name == 'short_description':
                        if 'required' in str(error).lower():
                            suggestions.append("Describe what makes your venue special")
                        elif 'length' in str(error).lower():
                            suggestions.append("Keep description between 50-500 characters")
                
                validation_result = {
                    'is_valid': False,
                    'errors': error_messages,
                    'suggestions': suggestions
                }

            return JsonResponse(validation_result)
        
        elif action == 'check_step_requirements':
            # Check if current step meets requirements for progression
            requirements = wizard.get_step_requirements(step)
            current_data = wizard.get_progress()
            
            completion_status = wizard.check_step_completion(step, current_data)
            
            return JsonResponse({
                'requirements': requirements,
                'completion_status': completion_status,
                'can_proceed': completion_status.get('complete', False),
                'missing_fields': completion_status.get('missing_fields', [])
            })
        
        elif action == 'get_progress_status':
            # Get overall progress and step completion status
            draft = wizard.get_draft()
            if draft:
                step_completion = wizard.get_step_completion_status()
                progress_percentage = draft.get_progress_percentage()
                
                return JsonResponse({
                    'success': True,
                    'progress_percentage': progress_percentage,
                    'step_completion': step_completion,
                    'completed_steps': draft.completed_steps,
                    'current_step': draft.current_step
                })
            else:
                return JsonResponse({
                    'success': False,
                    'message': 'No progress found'
                })

    return JsonResponse({'success': False, 'message': 'Invalid request'})


def create_venue_from_wizard(request, wizard, form):
    """Create venue from wizard data with enhanced error handling for 5-step wizard"""
    import logging
    logger = logging.getLogger(__name__)

    try:
        # Get the draft with all saved data
        draft = wizard.get_draft()
        if not draft:
            logger.warning(f"No draft data found for user {request.user.id}")
            messages.error(request, 'No draft data found. Please start the venue creation process again.')
            return redirect('venues_app:venue_create_wizard')

        # Validate draft data before attempting venue creation
        validation_errors = validate_draft_for_venue_creation(draft)
        if validation_errors:
            logger.warning(f"Draft validation failed for user {request.user.id}: {validation_errors}")
            for field, error_message in validation_errors.items():
                messages.error(request, f"{field.replace('_', ' ').title()}: {error_message}")
            return redirect('venues_app:venue_create_wizard', step='details')

        # Create venue from draft data
        venue, creation_errors = create_venue_from_draft(draft, form.cleaned_data)

        if venue:
            logger.info(f"Venue created successfully: {venue.id} for user {request.user.id}")
            # Check if user wants to submit for approval
            venue_status = form.cleaned_data.get('venue_status', 'draft')

            if venue_status == 'pending':
                # Validate venue meets approval requirements
                success, message, missing_requirements = venue.submit_for_approval()

                if not success:
                    # If validation fails, save as draft and show requirements
                    venue.approval_status = Venue.DRAFT
                    venue.save(update_fields=['approval_status'])

                    messages.warning(request, 'Venue saved as draft. Complete the missing requirements before submitting for approval.')

                    # Show missing requirements
                    if missing_requirements:
                        requirement_messages = []
                        for req in missing_requirements[:3]:  # Show first 3 requirements
                            requirement_messages.append(f"• {req['requirement']}: {req['description']}")

                        if len(missing_requirements) > 3:
                            requirement_messages.append(f"• And {len(missing_requirements) - 3} more requirements...")

                        messages.info(
                            request,
                            f"Missing requirements:\n" + "\n".join(requirement_messages)
                        )
                else:
                    # Venue was successfully submitted for approval
                    messages.success(request, message)
            else:
                # Save as draft
                venue.approval_status = Venue.DRAFT
                venue.save(update_fields=['approval_status'])
                messages.success(request, 'Venue saved as draft. You can submit it for approval when ready.')

            # Clear wizard progress
            wizard.clear_progress()

            # Mark tutorial as completed
            request.user.service_provider_profile.venue_creation_tutorial_completed = True
            request.user.service_provider_profile.save(update_fields=['venue_creation_tutorial_completed'])

            # Try auto-approval if venue meets criteria
            if venue.approval_status == Venue.PENDING:
                auto_approved = venue.apply_auto_approval_if_eligible()
                if auto_approved:
                    messages.success(
                        request,
                        '🎉 Congratulations! Your venue has been automatically approved and is now live! '
                        'Customers can now find and book your services.'
                    )
                else:
                    messages.success(
                        request,
                        '✅ Your venue has been submitted for review! We\'ll notify you once it\'s approved. '
                        'In the meantime, you can continue to enhance your venue profile.'
                    )
            else:
                messages.success(
                    request,
                    '📝 Your venue has been saved as a draft. Submit it for approval when you\'re ready!'
                )

            return redirect('venues_app:venue_progress')
        else:
            # Venue creation failed - show specific error messages
            logger.error(f"Venue creation failed for user {request.user.id}. Errors: {creation_errors}")

            if creation_errors:
                # Show specific field errors
                for field, error_message in creation_errors.items():
                    messages.error(request, f"{field.replace('_', ' ').title()}: {error_message}")
            else:
                messages.error(request, 'Failed to create venue. Please check your data and try again.')

            return redirect('venues_app:venue_create_wizard', step='details')

    except Exception as e:
        # Log the error for debugging
        logger.error(f"Unexpected error in create_venue_from_wizard for user {request.user.id}: {str(e)}", exc_info=True)

        # Use the error handler for user-friendly messages
        from ..utils import handle_venue_creation_error
        error_info = handle_venue_creation_error(e, user_context={'user_id': request.user.id, 'step': 'venue_creation'})

        messages.error(request, error_info['message'])
        if error_info.get('suggestions'):
            for suggestion in error_info['suggestions'][:2]:  # Show first 2 suggestions
                messages.info(request, f"💡 {suggestion}")

        return redirect('venues_app:venue_create_wizard', step='details')


def validate_draft_for_venue_creation(draft):
    """Validate draft data before attempting venue creation"""
    errors = {}

    # Check required basic fields
    if not draft.venue_name or not draft.venue_name.strip():
        errors['venue_name'] = "Venue name is required"
    elif len(draft.venue_name.strip()) < 2:
        errors['venue_name'] = "Venue name must be at least 2 characters long"

    if not draft.short_description or not draft.short_description.strip():
        errors['short_description'] = "Venue description is required"
    elif len(draft.short_description.strip()) < 10:
        errors['short_description'] = "Description must be at least 10 characters long"

    # Check required location fields
    if not draft.state or not draft.state.strip():
        errors['state'] = "State is required"

    if not draft.city or not draft.city.strip():
        errors['city'] = "City is required"

    # Check required contact fields
    if not draft.phone or not draft.phone.strip():
        errors['phone'] = "Phone number is required"

    if not draft.email or not draft.email.strip():
        errors['email'] = "Email address is required"

    # Validate email format
    if draft.email and draft.email.strip():
        import re
        email_pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        if not re.match(email_pattern, draft.email.strip()):
            errors['email'] = "Please enter a valid email address"

    # Check if service provider already has a venue
    if hasattr(draft.service_provider, 'venue') and draft.service_provider.venue and not draft.service_provider.venue.is_deleted:
        errors['venue_limit'] = "You can only create one venue per account"

    return errors


def create_venue_from_draft(draft, final_step_data):
    """Create a venue from the draft data with comprehensive error handling"""
    import logging
    from django.db import transaction
    from django.core.exceptions import ValidationError
    from django.db import IntegrityError

    logger = logging.getLogger(__name__)
    errors = {}

    try:
        with transaction.atomic():
            # Validate required fields before creation
            required_fields = {
                'venue_name': draft.venue_name,
                'short_description': draft.short_description,
                'state': draft.state,
                'city': draft.city,
                'phone': draft.phone,
                'email': draft.email,
            }

            for field_name, field_value in required_fields.items():
                if not field_value or (isinstance(field_value, str) and not field_value.strip()):
                    errors[field_name] = f"{field_name.replace('_', ' ').title()} is required"

            if errors:
                logger.warning(f"Required field validation failed: {errors}")
                return None, errors

            # Prepare venue data with safe defaults (only fields that exist in Venue model)
            venue_data = {
                'service_provider': draft.service_provider,
                'venue_name': draft.venue_name.strip(),
                'short_description': draft.short_description.strip(),
                'state': draft.state.strip() if draft.state else '',
                'county': draft.county.strip() if draft.county else '',
                'city': draft.city.strip() if draft.city else '',
                'street_number': draft.street_number.strip() if draft.street_number else '',
                'street_name': draft.street_name.strip() if draft.street_name else '',
                'latitude': draft.latitude,
                'longitude': draft.longitude,
                'phone': draft.phone.strip() if draft.phone else '',
                'email': draft.email.strip() if draft.email else '',
                'website_url': draft.website_url.strip() if draft.website_url else '',
                'instagram_url': draft.instagram_url.strip() if draft.instagram_url else '',
                'facebook_url': draft.facebook_url.strip() if draft.facebook_url else '',
                'twitter_url': draft.twitter_url.strip() if draft.twitter_url else '',
                'linkedin_url': draft.linkedin_url.strip() if draft.linkedin_url else '',
                'approval_status': Venue.DRAFT if final_step_data.get('venue_status') == 'draft' else Venue.PENDING
            }

            # Note: zip_code from draft is not included because Venue model doesn't have this field
            # The Venue model uses zip_codes (plural) field which is a TextField for multiple ZIP codes

            # Create the venue with validated data
            venue = Venue.objects.create(**venue_data)
            logger.info(f"Venue created successfully: {venue.id}")

            # Add categories with error handling
            if draft.categories_data:
                try:
                    from ..models import Category
                    categories = Category.objects.filter(id__in=draft.categories_data)
                    if categories.exists():
                        venue.categories.set(categories)
                        logger.info(f"Added {categories.count()} categories to venue {venue.id}")
                    else:
                        logger.warning(f"No valid categories found for venue {venue.id}")
                except Exception as e:
                    logger.error(f"Error adding categories to venue {venue.id}: {str(e)}")
                    errors['categories'] = "Failed to add categories"

            # Create operating hours with error handling
            if draft.operating_hours_data:
                try:
                    create_operating_hours_from_data(venue, draft.operating_hours_data)
                    logger.info(f"Created operating hours for venue {venue.id}")
                except Exception as e:
                    logger.error(f"Error creating operating hours for venue {venue.id}: {str(e)}")
                    errors['operating_hours'] = "Failed to create operating hours"

            # Create amenities with error handling
            if draft.amenities_data:
                try:
                    create_amenities_from_data(venue, draft.amenities_data)
                    logger.info(f"Created amenities for venue {venue.id}")
                except Exception as e:
                    logger.error(f"Error creating amenities for venue {venue.id}: {str(e)}")
                    errors['amenities'] = "Failed to create amenities"

            # Create services with error handling
            if draft.services_data:
                try:
                    create_services_from_data(venue, draft.services_data)
                    logger.info(f"Created services for venue {venue.id}")
                except Exception as e:
                    logger.error(f"Error creating services for venue {venue.id}: {str(e)}")
                    errors['services'] = "Failed to create services"

            # Create FAQs with error handling
            if draft.faqs_data:
                try:
                    create_faqs_from_data(venue, draft.faqs_data)
                    logger.info(f"Created FAQs for venue {venue.id}")
                except Exception as e:
                    logger.error(f"Error creating FAQs for venue {venue.id}: {str(e)}")
                    errors['faqs'] = "Failed to create FAQs"

            # Handle images with error handling
            if draft.images_data:
                try:
                    create_images_from_data(venue, draft.images_data)
                    logger.info(f"Created images for venue {venue.id}")
                except Exception as e:
                    logger.error(f"Error creating images for venue {venue.id}: {str(e)}")
                    errors['images'] = "Failed to upload images"

            # If there were any non-critical errors, log them but still return the venue
            if errors:
                logger.warning(f"Venue {venue.id} created with some errors: {errors}")

            return venue, errors

    except ValidationError as e:
        logger.error(f"Validation error creating venue: {str(e)}")
        if hasattr(e, 'error_dict'):
            errors.update(e.error_dict)
        else:
            errors['validation'] = str(e)
        return None, errors

    except IntegrityError as e:
        logger.error(f"Database integrity error creating venue: {str(e)}")
        if 'unique_venue_per_service_provider' in str(e):
            errors['venue_limit'] = "You can only create one venue per account"
        elif 'unique_venue_name_per_location' in str(e):
            errors['venue_name'] = "A venue with this name already exists in this location"
        else:
            errors['database'] = "Database constraint violation"
        return None, errors

    except Exception as e:
        logger.error(f"Unexpected error creating venue from draft: {str(e)}", exc_info=True)
        errors['unexpected'] = f"An unexpected error occurred: {str(e)}"
        return None, errors


def create_operating_hours_from_data(venue, hours_data):
    """Create operating hours from JSON data with validation"""
    from ..models import OperatingHours
    import logging

    logger = logging.getLogger(__name__)

    if not hours_data or not isinstance(hours_data, list):
        raise ValueError("Invalid operating hours data format")

    valid_days = ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']

    for day_data in hours_data:
        if not isinstance(day_data, dict):
            logger.warning(f"Skipping invalid day data for venue {venue.id}: {day_data}")
            continue

        day = day_data.get('day')
        if not day or day.lower() not in valid_days:
            logger.warning(f"Skipping invalid day '{day}' for venue {venue.id}")
            continue

        try:
            OperatingHours.objects.create(
                venue=venue,
                day=day.lower(),
                opening=day_data.get('opening'),
                closing=day_data.get('closing'),
                is_closed=day_data.get('is_closed', False),
                is_24_hours=day_data.get('is_24_hours', False)
            )
        except Exception as e:
            logger.error(f"Failed to create operating hours for {day} on venue {venue.id}: {str(e)}")
            raise


def create_amenities_from_data(venue, amenities_data):
    """Create amenities from JSON data with validation"""
    from ..models import VenueAmenity
    import logging

    logger = logging.getLogger(__name__)

    if not amenities_data or not isinstance(amenities_data, list):
        raise ValueError("Invalid amenities data format")

    # Get valid amenity choices from the model
    valid_amenities = [choice[0] for choice in VenueAmenity.AMENITY_CHOICES]

    for amenity_type in amenities_data:
        if not amenity_type or not isinstance(amenity_type, str):
            logger.warning(f"Skipping invalid amenity type for venue {venue.id}: {amenity_type}")
            continue

        if amenity_type not in valid_amenities:
            logger.warning(f"Skipping invalid amenity '{amenity_type}' for venue {venue.id}")
            continue

        try:
            VenueAmenity.objects.create(
                venue=venue,
                amenity_type=amenity_type
            )
        except Exception as e:
            logger.error(f"Failed to create amenity '{amenity_type}' for venue {venue.id}: {str(e)}")
            raise


def create_services_from_data(venue, services_data):
    """Create services from JSON data with validation"""
    from ..models import Service
    import logging
    from decimal import Decimal, InvalidOperation

    logger = logging.getLogger(__name__)

    if not services_data or not isinstance(services_data, list):
        raise ValueError("Invalid services data format")

    for service_data in services_data:
        if not isinstance(service_data, dict):
            logger.warning(f"Skipping invalid service data for venue {venue.id}: {service_data}")
            continue

        # Validate required fields
        title = service_data.get('title', '').strip()
        if not title:
            logger.warning(f"Skipping service with missing title for venue {venue.id}")
            continue

        description = service_data.get('description', '').strip()
        if not description:
            logger.warning(f"Skipping service '{title}' with missing description for venue {venue.id}")
            continue

        # Validate and convert price fields
        try:
            price_min = service_data.get('price_min')
            price_max = service_data.get('price_max')

            if price_min is not None:
                price_min = Decimal(str(price_min))
                if price_min < 0:
                    raise ValueError("Price cannot be negative")

            if price_max is not None:
                price_max = Decimal(str(price_max))
                if price_max < 0:
                    raise ValueError("Price cannot be negative")

            if price_min is not None and price_max is not None and price_min > price_max:
                logger.warning(f"Invalid price range for service '{title}' on venue {venue.id}: min > max")
                continue

        except (InvalidOperation, ValueError) as e:
            logger.warning(f"Invalid price data for service '{title}' on venue {venue.id}: {str(e)}")
            continue

        # Validate duration
        duration = service_data.get('duration', 60)
        try:
            duration = int(duration)
            if duration <= 0:
                duration = 60  # Default to 60 minutes
        except (ValueError, TypeError):
            duration = 60

        try:
            Service.objects.create(
                venue=venue,
                service_title=title,
                short_description=description,
                price_min=price_min,
                price_max=price_max,
                duration_minutes=duration
            )
        except Exception as e:
            logger.error(f"Failed to create service '{title}' for venue {venue.id}: {str(e)}")
            raise


def create_faqs_from_data(venue, faqs_data):
    """Create FAQs from JSON data with validation"""
    from ..models import VenueFAQ
    import logging

    logger = logging.getLogger(__name__)

    if not faqs_data or not isinstance(faqs_data, list):
        raise ValueError("Invalid FAQs data format")

    valid_faqs = []
    for i, faq_data in enumerate(faqs_data):
        if not isinstance(faq_data, dict):
            logger.warning(f"Skipping invalid FAQ data for venue {venue.id}: {faq_data}")
            continue

        question = faq_data.get('question', '').strip()
        answer = faq_data.get('answer', '').strip()

        if not question:
            logger.warning(f"Skipping FAQ with missing question for venue {venue.id}")
            continue

        if not answer:
            logger.warning(f"Skipping FAQ with missing answer for venue {venue.id}")
            continue

        # Validate length constraints
        if len(question) > 255:
            logger.warning(f"Truncating FAQ question for venue {venue.id} (too long)")
            question = question[:255]

        if len(answer) > 500:
            logger.warning(f"Truncating FAQ answer for venue {venue.id} (too long)")
            answer = answer[:500]

        valid_faqs.append({
            'question': question,
            'answer': answer,
            'order': len(valid_faqs) + 1
        })

    # Create FAQs
    for faq_data in valid_faqs:
        try:
            VenueFAQ.objects.create(
                venue=venue,
                question=faq_data['question'],
                answer=faq_data['answer'],
                order=faq_data['order']
            )
        except Exception as e:
            logger.error(f"Failed to create FAQ for venue {venue.id}: {str(e)}")
            raise


def create_images_from_data(venue, images_data):
    """Create images from uploaded data during venue creation with validation"""
    import logging
    from ..models import VenueImage
    import os
    from django.core.files.base import ContentFile
    from django.core.files.storage import default_storage

    logger = logging.getLogger(__name__)

    if not images_data or not isinstance(images_data, list):
        logger.warning(f"No valid images data provided for venue {venue.id}")
        return

    created_images = []
    max_images = 5  # Venue image limit

    for i, img_data in enumerate(images_data):
        if i >= max_images:
            logger.warning(f"Skipping image {i+1} for venue {venue.id} - maximum {max_images} images allowed")
            break

        if not isinstance(img_data, dict) or 'url' not in img_data:
            logger.warning(f"Skipping invalid image data for venue {venue.id}: {img_data}")
            continue
            
        try:
            url = img_data.get('url', '').strip()
            if not url:
                logger.warning(f"Skipping image with empty URL for venue {venue.id}")
                continue

            # Validate order value
            order = img_data.get('order', i + 1)
            try:
                order = int(order)
                if order < 1 or order > 5:
                    order = i + 1
            except (ValueError, TypeError):
                order = i + 1

            # Validate caption length
            caption = img_data.get('name', '').strip()
            if len(caption) > 255:
                caption = caption[:255]
                logger.warning(f"Truncated image caption for venue {venue.id}")

            if url.startswith('data:image/'):
                # Handle base64 data URL
                try:
                    import base64
                    import uuid
                    from mimetypes import guess_extension

                    # Validate data URL format
                    if ',' not in url:
                        logger.error(f"Invalid data URL format for venue {venue.id}")
                        continue

                    # Parse the data URL
                    header, data = url.split(',', 1)
                    if ';' not in header or ':' not in header:
                        logger.error(f"Invalid data URL header for venue {venue.id}")
                        continue

                    mime_type = header.split(';')[0].split(':')[1]

                    # Validate image type
                    if not mime_type.startswith('image/'):
                        logger.error(f"Invalid image type '{mime_type}' for venue {venue.id}")
                        continue

                    # Decode the base64 data
                    image_data = base64.b64decode(data)

                    # Check file size (5MB limit)
                    if len(image_data) > 5 * 1024 * 1024:
                        logger.error(f"Image too large for venue {venue.id}: {len(image_data)} bytes")
                        continue

                    # Generate a unique filename
                    extension = guess_extension(mime_type) or '.jpg'
                    filename = f"venue_{venue.id}_{uuid.uuid4().hex[:8]}{extension}"

                    # Save the file
                    file_path = default_storage.save(
                        f"venues/{venue.id}/gallery/{filename}",
                        ContentFile(image_data)
                    )

                    # Create the VenueImage object
                    venue_image = VenueImage.objects.create(
                        venue=venue,
                        image=file_path,
                        order=order,
                        caption=caption
                    )

                    created_images.append(venue_image)
                    logger.info(f"Created image from data URL for venue {venue.id}")

                except Exception as e:
                    logger.error(f"Failed to process data URL image for venue {venue.id}: {str(e)}")
                    continue

            elif url.startswith('/media/') or url.startswith('http'):
                # Handle existing image URL
                try:
                    image_path = url.replace('/media/', '') if url.startswith('/media/') else url

                    venue_image = VenueImage.objects.create(
                        venue=venue,
                        image=image_path,
                        order=order,
                        caption=caption
                    )

                    created_images.append(venue_image)
                    logger.info(f"Created image from URL for venue {venue.id}")

                except Exception as e:
                    logger.error(f"Failed to create image from URL for venue {venue.id}: {str(e)}")
                    continue
            else:
                logger.warning(f"Unsupported image URL format for venue {venue.id}: {url}")
                continue

        except Exception as e:
            logger.error(f"Unexpected error creating venue image for venue {venue.id}: {str(e)}")
            continue
    
    # Set the first image as primary if no primary image is set
    if created_images:
        # Check if any image is already marked as primary
        has_primary = any(hasattr(img, 'is_primary') and img.is_primary for img in created_images)
        if not has_primary:
            try:
                # Set first image as primary
                first_image = created_images[0]
                first_image.order = 1
                first_image.save(update_fields=['order'])
                logger.info(f"Set first image as primary for venue {venue.id}")
            except Exception as e:
                logger.error(f"Failed to set primary image for venue {venue.id}: {str(e)}")

    logger.info(f"Successfully created {len(created_images)} images for venue {venue.id}")
    return created_images


def get_guided_tour_steps(current_step):
    """Get guided tour steps for venue creation"""
    tour_steps = {
        'basic': [
            {
                'target': '#venue_name',
                'title': 'Venue Name',
                'content': 'Choose a memorable name that represents your business. This will be visible to customers.',
                'placement': 'bottom'
            },
            {
                'target': '#short_description',
                'title': 'Description',
                'content': 'Write a brief, compelling description of your venue and services. This helps customers understand what you offer.',
                'placement': 'bottom'
            }
        ],
        'location': [
            {
                'target': '#state',
                'title': 'Location Details',
                'content': 'Provide accurate location information. This helps customers find you and enables our location-based features.',
                'placement': 'bottom'
            }
        ],
        'categories': [
            {
                'target': '.category-selection-grid',
                'title': 'Categories',
                'content': 'Select up to 3 categories that best describe your services. This helps customers find you in search results.',
                'placement': 'top'
            }
        ],
        'location': [
            {
                'target': '#phone',
                'title': 'Contact Information',
                'content': 'Adding contact information is optional but helps build trust with customers and improves your venue\'s visibility.',
                'placement': 'bottom'
            }
        ],
        'final': [
            {
                'target': '.venue-status-options',
                'title': 'Save or Submit',
                'content': 'Choose to save as draft for later editing, or submit for admin approval to make your venue visible to customers.',
                'placement': 'top'
            }
        ]
    }
    
    return tour_steps.get(current_step, [])


@login_required
def venue_create_view(request):
    """Venue creation view - check for existing venue before redirecting to wizard"""
    # Ensure user has service provider profile
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'You must have a service provider profile to create a venue.')
        return redirect('accounts_app:service_provider_profile')

    # Check if user already has a venue
    existing_venue = Venue.objects.filter(
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    ).first()

    if existing_venue:
        messages.info(request, 'You already have a venue. You can only have one venue per account. You can edit your existing venue or manage its details below.')
        return redirect('venues_app:venue_edit')

    # If no existing venue, redirect to wizard
    return redirect('venues_app:venue_create_wizard_default')


class VenueCreateView(ServiceProviderRequiredMixin, CreateView):
    """Allow service providers to create a new venue."""
    model = Venue
    form_class = VenueForm
    template_name = 'venues_app/venue_create.html'
    def get_success_url(self):
        return reverse_lazy('venues_app:provider_venue_detail', kwargs={'venue_id': self.object.id})

    def dispatch(self, request, *args, **kwargs):
        # Ensure user has service provider profile
        if not hasattr(request.user, 'service_provider_profile'):
            messages.error(request, 'You must have a service provider profile to create a venue.')
            return redirect('accounts_app:service_provider_profile')

        # Check if user already has a venue (more robust check)
        existing_venue = Venue.objects.filter(
            service_provider=request.user.service_provider_profile,
            is_deleted=False
        ).first()

        if existing_venue:
            messages.info(request, 'You already have a venue. You can only have one venue per account. You can edit your existing venue or manage its details below.')
            return redirect('venues_app:venue_edit')

        return super().dispatch(request, *args, **kwargs)

    def get_form_kwargs(self):
        kwargs = super().get_form_kwargs()
        kwargs['user'] = self.request.user
        return kwargs

    def form_valid(self, form):
        try:
            # Double-check no venue exists before saving
            existing_venue = Venue.objects.filter(
                service_provider=self.request.user.service_provider_profile,
                is_deleted=False
            ).first()

            if existing_venue:
                messages.error(self.request, 'You already have a venue. Cannot create multiple venues.')
                return redirect('venues_app:venue_edit')

            # Save the venue
            response = super().form_valid(form)
            venue = self.object

            # Check if user wants to submit for approval
            venue_status = form.cleaned_data.get('venue_status', 'draft')

            if venue_status == 'pending':
                # Validate venue meets approval requirements
                success, message, missing_requirements = venue.submit_for_approval()

                if not success:
                    # If validation fails, save as draft and show requirements
                    venue.approval_status = Venue.DRAFT
                    venue.save(update_fields=['approval_status'])

                    messages.warning(self.request, 'Venue saved as draft. Complete the missing requirements before submitting for approval.')

                    # Show missing requirements
                    if missing_requirements:
                        requirement_messages = []
                        for req in missing_requirements[:3]:  # Show first 3 requirements
                            requirement_messages.append(f"• {req['requirement']}: {req['description']}")

                        if len(missing_requirements) > 3:
                            requirement_messages.append(f"• And {len(missing_requirements) - 3} more requirements...")

                        messages.info(
                            self.request,
                            f"Missing requirements:\n" + "\n".join(requirement_messages)
                        )
                else:
                    # Venue was successfully submitted for approval
                    messages.success(self.request, message)
            else:
                # Save as draft
                venue.approval_status = Venue.DRAFT
                venue.save(update_fields=['approval_status'])
                messages.success(self.request, 'Venue saved as draft. You can submit it for approval when ready.')

            messages.success(
                self.request,
                f'Your venue "{venue.venue_name}" has been created successfully and is pending admin approval. '
                'You will be notified once it is reviewed.'
            )
            return response

        except Exception as e:
            messages.error(self.request, 'There was an error creating your venue. Please try again.')
            return self.form_invalid(form)

    def form_invalid(self, form):
        messages.error(
            self.request,
            'Please correct the errors below and try again. All required fields must be filled out.'
        )
        return super().form_invalid(form)


@login_required
def venue_edit_view(request):
    """Redirect to venue edit wizard - the primary way to edit venues."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit venues.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    # Always redirect to the wizard - this is the primary editing method
    return redirect('venues_app:venue_edit_wizard_default')


def handle_venue_edit_operation(request, venue, form):
    """Handle venue edit operation with enhanced validation"""
    if form.is_valid():
        # Check for significant changes that might require re-approval
        changed_fields = []
        for field_name, field_value in form.cleaned_data.items():
            if hasattr(venue, field_name):
                old_value = getattr(venue, field_name)
                if old_value != field_value:
                    changed_fields.append(field_name)
        
        # Save the venue
        updated_venue = form.save()
        
        # Check if reapproval is needed
        if updated_venue.check_reapproval_required(changed_fields):
            updated_venue.trigger_reapproval(
                f"Significant changes made to: {', '.join(changed_fields)}"
            )
            messages.info(
                request,
                '📋 Your changes have been saved, but your venue needs re-approval due to significant updates. '
                'It will remain visible while under review.'
            )
        
        return {'success': True, 'venue': updated_venue}
    else:
        return {'success': False, 'errors': form.errors}


class VenueDeleteView(ServiceProviderRequiredMixin, DeleteView):
    """Allow service providers to delete their venue."""
    model = Venue
    template_name = 'venues_app/venue_delete.html'
    success_url = reverse_lazy('venues_app:venue_create')

    def get_object(self, queryset=None):
        try:
            return self.request.user.service_provider_profile.venue
        except Venue.DoesNotExist:
            messages.error(self.request, 'You do not have a venue to delete.')
            raise

    def delete(self, request, *args, **kwargs):
        messages.success(request, 'Your venue has been deleted successfully.')
        return super().delete(request, *args, **kwargs)


# Function-based aliases
venue_create = venue_create_view
venue_edit = venue_edit_view
venue_delete = VenueDeleteView.as_view()


@login_required
def manage_services(request):
    """Allow service providers to manage their venue services."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before managing services.')
        return redirect('venues_app:venue_create')

    services = venue.services.all().select_related('service_category').order_by('service_category__name', 'service_title')
    
    # Get service categories for filtering
    from ..models import ServiceCategory
    service_categories = ServiceCategory.objects.filter(is_active=True).order_by('sort_order', 'name')
    
    # Calculate statistics
    services_active_count = services.filter(is_active=True).count()
    services_featured_count = services.filter(is_featured=True).count()
    services_categories_count = services.values('service_category').distinct().count()

    context = {
        'venue': venue,
        'services': services,
        'service_categories': service_categories,
        'max_services': MAX_SERVICES_PER_VENUE,
        'can_add_service': services.count() < MAX_SERVICES_PER_VENUE,
        'services_active_count': services_active_count,
        'services_featured_count': services_featured_count,
        'services_categories_count': services_categories_count,
    }
    return render(request, 'venues_app/manage_services.html', context)


@login_required
def manage_faqs(request):
    """Allow service providers to manage their venue FAQs with templates and proper ordering."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before adding FAQs.')
        return redirect('venues_app:venue_create')

    faqs = venue.faqs.all().order_by('order')
    can_add_faq = faqs.count() < MAX_FAQS_PER_VENUE

    # Get available FAQ templates based on venue categories
    faq_templates = []
    if venue.categories.exists():
        faq_templates = VenueFAQForm.get_template_by_venue_category(venue.categories.all())

    if request.method == 'POST':
        # Handle template application
        if 'apply_template' in request.POST:
            template_index = request.POST.get('template_index')
            if template_index and template_index.isdigit() and faq_templates:
                try:
                    template_index = int(template_index)
                    if 0 <= template_index < len(faq_templates):
                        template = faq_templates[template_index]
                        form = VenueFAQForm(initial=template)
                        messages.info(request, 'Template applied. Review and save your FAQ.')
                    else:
                        form = VenueFAQForm()
                        messages.error(request, 'Invalid template selected.')
                except (ValueError, IndexError):
                    form = VenueFAQForm()
                    messages.error(request, 'Invalid template selected.')
            else:
                form = VenueFAQForm()
                messages.warning(request, 'Please select a template to apply.')
        
        # Handle FAQ creation
        elif can_add_faq:
            form = VenueFAQForm(request.POST)
            if form.is_valid():
                faq = form.save(commit=False)
                faq.venue = venue

                # Improved ordering: find next available order or compress gaps
                existing_orders = list(venue.faqs.values_list('order', flat=True).order_by('order'))
                
                # Find the next order (fill gaps first, then increment)
                next_order = 1
                for order in existing_orders:
                    if next_order < order:
                        break  # Found a gap
                    next_order = order + 1
                
                # Ensure we don't exceed the maximum
                if next_order > MAX_FAQS_PER_VENUE:
                    messages.error(request, f'Maximum {MAX_FAQS_PER_VENUE} FAQs allowed per venue.')
                    return redirect('venues_app:manage_faqs')

                faq.order = next_order
                faq.save()
                
                # Reorder FAQs to compress gaps
                _reorder_faqs(venue)
                
                messages.success(request, 'FAQ added successfully.')
                return redirect('venues_app:manage_faqs')
        else:
            form = VenueFAQForm()
            messages.error(request, f'Maximum {MAX_FAQS_PER_VENUE} FAQs allowed per venue.')
    else:
        form = VenueFAQForm()

    context = {
        'venue': venue,
        'faqs': faqs,
        'form': form,
        'max_faqs': MAX_FAQS_PER_VENUE,
        'can_add_faq': can_add_faq,
        'faq_templates': faq_templates,
        'has_templates': bool(faq_templates),
    }
    return render(request, 'venues_app/manage_faqs.html', context)


def _reorder_faqs(venue):
    """Reorder FAQs to eliminate gaps and ensure sequential ordering."""
    faqs = venue.faqs.all().order_by('order')
    for index, faq in enumerate(faqs, 1):
        if faq.order != index:
            faq.order = index
            faq.save(update_fields=['order'])


@login_required
def delete_faq(request, faq_id):
    """Allow service providers to delete their venue FAQs with automatic reordering."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can delete FAQs.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        faq = get_object_or_404(VenueFAQ, id=faq_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        faq.delete()
        
        # Reorder remaining FAQs to eliminate gaps
        _reorder_faqs(venue)
        
        messages.success(request, 'FAQ deleted successfully.')
        return redirect('venues_app:manage_faqs')

    context = {
        'venue': venue,
        'faq': faq,
    }
    return render(request, 'venues_app/faq_delete.html', context)


@login_required 
def reorder_faqs(request):
    """Allow service providers to manually reorder their FAQs via AJAX."""
    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Permission denied'})

    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Invalid request method'})

    try:
        venue = request.user.service_provider_profile.venue
        faq_orders = json.loads(request.body)
        
        # Validate the order data
        if not isinstance(faq_orders, list):
            return JsonResponse({'success': False, 'error': 'Invalid data format'})
        
        # Update FAQ orders
        with transaction.atomic():
            for item in faq_orders:
                faq_id = item.get('id')
                new_order = item.get('order')
                
                if not faq_id or not new_order:
                    continue
                    
                try:
                    faq = VenueFAQ.objects.get(id=faq_id, venue=venue)
                    faq.order = new_order
                    faq.save(update_fields=['order'])
                except VenueFAQ.DoesNotExist:
                    continue
        
        return JsonResponse({'success': True, 'message': 'FAQs reordered successfully'})
        
    except (json.JSONDecodeError, Venue.DoesNotExist):
        return JsonResponse({'success': False, 'error': 'Invalid request'})
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def manage_operating_hours(request):
    """Allow service providers to manage their venue operating hours with templates."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can manage operating hours.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before setting operating hours.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = SimplifiedOperatingHoursForm(request.POST, venue=venue)
        
        # Check if applying a template
        if 'apply_template' in request.POST:
            template_type = request.POST.get('template', '')
            if template_type:
                # Apply the selected template
                template_data = _get_template_data(template_type)
                form = SimplifiedOperatingHoursForm(initial=template_data, venue=venue)
                messages.info(request, f'Applied {template_type.replace("_", " ").title()} template. Review and save your changes.')
            else:
                messages.warning(request, 'Please select a template to apply.')
                
        elif form.is_valid():
            # Save the operating hours
            if form.save(venue):
                messages.success(request, 'Operating hours updated successfully.')
                return redirect('venues_app:provider_venue_detail', venue_id=venue.id)
            else:
                messages.error(request, 'There was an error saving your operating hours.')
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = SimplifiedOperatingHoursForm(venue=venue)

    context = {
        'venue': venue,
        'form': form,
        'templates': _get_available_templates(),
    }
    return render(request, 'venues_app/manage_operating_hours.html', context)


def _get_available_templates():
    """Get available schedule templates with descriptions."""
    return [
        {
            'key': 'business_hours',
            'name': 'Business Hours',
            'description': 'Monday-Friday 9 AM - 5 PM, Closed weekends',
            'preview': 'Mon-Fri: 9:00 AM - 5:00 PM, Sat-Sun: Closed'
        },
        {
            'key': 'retail_hours',
            'name': 'Retail Hours',
            'description': 'Monday-Saturday 10 AM - 8 PM, Sunday 12 PM - 6 PM',
            'preview': 'Mon-Sat: 10:00 AM - 8:00 PM, Sun: 12:00 PM - 6:00 PM'
        },
        {
            'key': 'spa_hours',
            'name': 'Spa Hours',
            'description': 'Monday-Saturday 9 AM - 7 PM, Sunday 10 AM - 6 PM',
            'preview': 'Mon-Sat: 9:00 AM - 7:00 PM, Sun: 10:00 AM - 6:00 PM'
        },
        {
            'key': 'restaurant_hours',
            'name': 'Restaurant Hours',
            'description': 'Daily 11 AM - 10 PM',
            'preview': 'Daily: 11:00 AM - 10:00 PM'
        },
        {
            'key': 'gym_hours',
            'name': 'Gym Hours',
            'description': 'Monday-Friday 5 AM - 11 PM, Weekends 7 AM - 9 PM',
            'preview': 'Mon-Fri: 5:00 AM - 11:00 PM, Sat-Sun: 7:00 AM - 9:00 PM'
        },
        {
            'key': 'salon_hours',
            'name': 'Salon Hours',
            'description': 'Tuesday-Saturday 9 AM - 6 PM, Closed Sunday-Monday',
            'preview': 'Tue-Sat: 9:00 AM - 6:00 PM, Sun-Mon: Closed'
        },
        {
            'key': 'medical_hours',
            'name': 'Medical Hours',
            'description': 'Monday-Friday 8 AM - 6 PM, Saturday 9 AM - 1 PM',
            'preview': 'Mon-Fri: 8:00 AM - 6:00 PM, Sat: 9:00 AM - 1:00 PM, Sun: Closed'
        },
        {
            'key': 'extended_hours',
            'name': 'Extended Hours',
            'description': 'Daily 7 AM - 10 PM',
            'preview': 'Daily: 7:00 AM - 10:00 PM'
        },
        {
            'key': '24_7',
            'name': '24/7',
            'description': 'Always open, 24 hours a day',
            'preview': 'Daily: 24 Hours'
        },
    ]


def _get_template_data(template_type):
    """Get initial form data for a specific template."""
    templates = {
        'business_hours': {
            'monday_status': 'regular',
            'monday_opening': '09:00',
            'monday_closing': '17:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '09:00',
            'tuesday_closing': '17:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '09:00',
            'wednesday_closing': '17:00',
            'thursday_status': 'regular',
            'thursday_opening': '09:00',
            'thursday_closing': '17:00',
            'friday_status': 'regular',
            'friday_opening': '09:00',
            'friday_closing': '17:00',
            'saturday_status': 'closed',
            'sunday_status': 'closed',
        },
        'retail_hours': {
            'monday_status': 'regular',
            'monday_opening': '10:00',
            'monday_closing': '20:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '10:00',
            'tuesday_closing': '20:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '10:00',
            'wednesday_closing': '20:00',
            'thursday_status': 'regular',
            'thursday_opening': '10:00',
            'thursday_closing': '20:00',
            'friday_status': 'regular',
            'friday_opening': '10:00',
            'friday_closing': '20:00',
            'saturday_status': 'regular',
            'saturday_opening': '10:00',
            'saturday_closing': '20:00',
            'sunday_status': 'regular',
            'sunday_opening': '12:00',
            'sunday_closing': '18:00',
        },
        'spa_hours': {
            'monday_status': 'regular',
            'monday_opening': '09:00',
            'monday_closing': '19:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '09:00',
            'tuesday_closing': '19:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '09:00',
            'wednesday_closing': '19:00',
            'thursday_status': 'regular',
            'thursday_opening': '09:00',
            'thursday_closing': '19:00',
            'friday_status': 'regular',
            'friday_opening': '09:00',
            'friday_closing': '19:00',
            'saturday_status': 'regular',
            'saturday_opening': '09:00',
            'saturday_closing': '19:00',
            'sunday_status': 'regular',
            'sunday_opening': '10:00',
            'sunday_closing': '18:00',
        },
        'restaurant_hours': {
            'monday_status': 'regular',
            'monday_opening': '11:00',
            'monday_closing': '22:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '11:00',
            'tuesday_closing': '22:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '11:00',
            'wednesday_closing': '22:00',
            'thursday_status': 'regular',
            'thursday_opening': '11:00',
            'thursday_closing': '22:00',
            'friday_status': 'regular',
            'friday_opening': '11:00',
            'friday_closing': '22:00',
            'saturday_status': 'regular',
            'saturday_opening': '11:00',
            'saturday_closing': '22:00',
            'sunday_status': 'regular',
            'sunday_opening': '11:00',
            'sunday_closing': '22:00',
        },
        'gym_hours': {
            'monday_status': 'regular',
            'monday_opening': '05:00',
            'monday_closing': '23:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '05:00',
            'tuesday_closing': '23:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '05:00',
            'wednesday_closing': '23:00',
            'thursday_status': 'regular',
            'thursday_opening': '05:00',
            'thursday_closing': '23:00',
            'friday_status': 'regular',
            'friday_opening': '05:00',
            'friday_closing': '23:00',
            'saturday_status': 'regular',
            'saturday_opening': '07:00',
            'saturday_closing': '21:00',
            'sunday_status': 'regular',
            'sunday_opening': '07:00',
            'sunday_closing': '21:00',
        },
        'salon_hours': {
            'monday_status': 'closed',
            'tuesday_status': 'regular',
            'tuesday_opening': '09:00',
            'tuesday_closing': '18:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '09:00',
            'wednesday_closing': '18:00',
            'thursday_status': 'regular',
            'thursday_opening': '09:00',
            'thursday_closing': '18:00',
            'friday_status': 'regular',
            'friday_opening': '09:00',
            'friday_closing': '18:00',
            'saturday_status': 'regular',
            'saturday_opening': '09:00',
            'saturday_closing': '18:00',
            'sunday_status': 'closed',
        },
        'medical_hours': {
            'monday_status': 'regular',
            'monday_opening': '08:00',
            'monday_closing': '18:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '08:00',
            'tuesday_closing': '18:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '08:00',
            'wednesday_closing': '18:00',
            'thursday_status': 'regular',
            'thursday_opening': '08:00',
            'thursday_closing': '18:00',
            'friday_status': 'regular',
            'friday_opening': '08:00',
            'friday_closing': '18:00',
            'saturday_status': 'regular',
            'saturday_opening': '09:00',
            'saturday_closing': '13:00',
            'sunday_status': 'closed',
        },
        'extended_hours': {
            'monday_status': 'regular',
            'monday_opening': '07:00',
            'monday_closing': '22:00',
            'tuesday_status': 'regular',
            'tuesday_opening': '07:00',
            'tuesday_closing': '22:00',
            'wednesday_status': 'regular',
            'wednesday_opening': '07:00',
            'wednesday_closing': '22:00',
            'thursday_status': 'regular',
            'thursday_opening': '07:00',
            'thursday_closing': '22:00',
            'friday_status': 'regular',
            'friday_opening': '07:00',
            'friday_closing': '22:00',
            'saturday_status': 'regular',
            'saturday_opening': '07:00',
            'saturday_closing': '22:00',
            'sunday_status': 'regular',
            'sunday_opening': '07:00',
            'sunday_closing': '22:00',
        },
        '24_7': {
            'monday_status': '24hours',
            'tuesday_status': '24hours',
            'wednesday_status': '24hours',
            'thursday_status': '24hours',
            'friday_status': '24hours',
            'saturday_status': '24hours',
            'sunday_status': '24hours',
        },
    }
    
    return templates.get(template_type, {})


@login_required
def manage_amenities(request):
    """Allow service providers to manage their venue amenities."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can manage amenities.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before adding amenities.')
        return redirect('venues_app:venue_create')

    amenities = venue.amenities.all().order_by('amenity_type')
    can_add_amenity = amenities.count() < 15

    if request.method == 'POST' and can_add_amenity:
        form = VenueAmenityForm(request.POST)
        if form.is_valid():
            amenity = form.save(commit=False)
            amenity.venue = venue
            amenity.save()
            messages.success(request, 'Amenity added successfully.')
            return redirect('venues_app:manage_amenities')
    else:
        form = VenueAmenityForm()

    context = {
        'venue': venue,
        'amenities': amenities,
        'form': form,
        'can_add_amenity': can_add_amenity,
        'max_amenities': 15,
    }
    return render(request, 'venues_app/manage_amenities.html', context)


@login_required
def edit_faq(request, faq_id):
    """Allow service providers to edit their venue FAQs."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit FAQs.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        faq = get_object_or_404(VenueFAQ, id=faq_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = VenueFAQForm(request.POST, instance=faq)
        if form.is_valid():
            form.save()
            messages.success(request, 'FAQ updated successfully.')
            return redirect('venues_app:manage_faqs')
    else:
        form = VenueFAQForm(instance=faq)

    context = {
        'venue': venue,
        'faq': faq,
        'form': form,
        'action': 'Edit',
    }
    return render(request, 'venues_app/faq_form.html', context)


@login_required
def delete_faq(request, faq_id):
    """Allow service providers to delete their venue FAQs with automatic reordering."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can delete FAQs.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        faq = get_object_or_404(VenueFAQ, id=faq_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        faq.delete()
        
        # Reorder remaining FAQs to eliminate gaps
        _reorder_faqs(venue)
        
        messages.success(request, 'FAQ deleted successfully.')
        return redirect('venues_app:manage_faqs')

    context = {
        'venue': venue,
        'faq': faq,
    }
    return render(request, 'venues_app/faq_delete.html', context)


@login_required
def edit_amenity(request, amenity_id):
    """Allow service providers to edit their venue amenities."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit amenities.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        amenity = get_object_or_404(VenueAmenity, id=amenity_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = VenueAmenityForm(request.POST, instance=amenity)
        if form.is_valid():
            form.save()
            messages.success(request, 'Amenity updated successfully.')
            return redirect('venues_app:manage_amenities')
    else:
        form = VenueAmenityForm(instance=amenity)

    context = {
        'venue': venue,
        'amenity': amenity,
        'form': form,
        'action': 'Edit',
    }
    return render(request, 'venues_app/amenity_form.html', context)


@login_required
def delete_amenity(request, amenity_id):
    """Allow service providers to delete their venue amenities."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can delete amenities.')
        return redirect('home')

    try:
        venue = request.user.service_provider_profile.venue
        amenity = get_object_or_404(VenueAmenity, id=amenity_id, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        amenity.delete()
        messages.success(request, 'Amenity deleted successfully.')
        return redirect('venues_app:manage_amenities')

    context = {
        'venue': venue,
        'amenity': amenity,
    }
    return render(request, 'venues_app/amenity_delete.html', context)


@login_required
def provider_venues(request):
    """Display the provider's venue management dashboard (single venue per provider)."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('home')

    # Get the provider's venue (should be only one)
    venue = Venue.objects.filter(
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    ).prefetch_related('images', 'services', 'reviews').first()

    # If venue exists, redirect to venue detail page for management
    if venue:
        return redirect('venues_app:provider_venue_detail', venue_id=venue.id)

    # If no venue exists, show the venues list page with create option
    total_services = 0
    total_bookings = 0
    avg_rating = 0

    context = {
        'venues': [],
        'page_obj': None,
        'total_services': total_services,
        'total_bookings': total_bookings,
        'avg_rating': avg_rating,
    }
    return render(request, 'venues_app/provider/venues_list.html', context)


@login_required
def provider_venue_detail(request, venue_id):
    """Display detailed information about a specific venue for the provider."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('home')

    venue = get_object_or_404(
        Venue.objects.select_related('service_provider', 'service_provider__user')
        .prefetch_related('services', 'faqs', 'images', 'categories', 'amenities'),
        id=venue_id,
        service_provider=request.user.service_provider_profile
    )

    services = venue.services.all().order_by('service_title')
    opening_hours = venue.operating_hours_set.all().order_by('day')
    faqs = venue.faqs.all().order_by('order')
    images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    primary_image = images.filter(is_primary=True).first()
    gallery_images = images.filter(is_primary=False)
    amenities = venue.amenities.filter(is_active=True).order_by('amenity_type')

    # Calculate price range from services
    price_range = None
    if services.exists():
        prices = [service.price_min for service in services if service.price_min]
        if prices:
            min_price = min(prices)
            max_prices = [service.price_max or service.price_min for service in services if service.price_min]
            max_price = max(max_prices) if max_prices else min_price
            price_range = f"${min_price}" if min_price == max_price else f"${min_price} - ${max_price}"

    # Check if can add more FAQs
    can_add_faq = faqs.count() < MAX_FAQS_PER_VENUE

    # Check if venue is ready for approval (has minimum required content)
    is_ready_for_approval = (
        venue.venue_name and
        venue.short_description and
        services.exists() and
        images.exists()
    )

    # Get enhanced approval workflow data
    approval_timeline = venue.get_approval_timeline()
    impact_preview = venue.get_approval_impact_preview()
    approval_guidance = venue.get_approval_guidance()

    # Get approval requirements and eligibility
    approval_progress = venue.get_approval_requirements()
    can_submit_for_approval = venue.can_submit_for_approval()

    # Calculate venue completeness score
    venue_completeness_score = venue.calculate_completeness_score()
    
    # Calculate profile completeness score
    provider_profile = venue.service_provider
    profile_completeness_score = 0
    profile_missing_info = []
    
    # Profile completeness calculation
    profile_items = 0
    completed_profile_items = 0
    
    # Business information
    profile_items += 1
    if provider_profile.legal_name:
        completed_profile_items += 1
    else:
        profile_missing_info.append({'title': 'Business Name', 'message': 'Add your legal business name', 'priority': 'high'})
    
    profile_items += 1
    if provider_profile.address and provider_profile.city and provider_profile.state:
        completed_profile_items += 1
    else:
        profile_missing_info.append({'title': 'Business Address', 'message': 'Add your complete business address', 'priority': 'high'})
    
    profile_items += 1
    if provider_profile.phone:
        completed_profile_items += 1
    else:
        profile_missing_info.append({'title': 'Business Phone', 'message': 'Add your business phone number', 'priority': 'medium'})
    
    profile_items += 1
    if provider_profile.user.email:
        completed_profile_items += 1
    else:
        profile_missing_info.append({'title': 'Business Email', 'message': 'Add your business email address', 'priority': 'medium'})
    
    # Social media and website
    profile_items += 1
    if provider_profile.website or provider_profile.instagram or provider_profile.facebook:
        completed_profile_items += 1
    else:
        profile_missing_info.append({'title': 'Social Media/Website', 'message': 'Add your website or social media links', 'priority': 'low'})
    
    # Team members
    profile_items += 1
    if provider_profile.team.filter(is_active=True).exists():
        completed_profile_items += 1
    else:
        profile_missing_info.append({'title': 'Team Members', 'message': 'Add team members to help manage your business', 'priority': 'low'})
    
    profile_completeness_score = int((completed_profile_items / profile_items) * 100) if profile_items > 0 else 0
    
    # Get venue missing information for guidance
    venue_missing_info = []
    if not venue.venue_name:
        venue_missing_info.append({'title': 'Venue Name', 'message': 'Add a name for your venue', 'priority': 'high'})
    if not venue.short_description or len(venue.short_description) < 50:
        venue_missing_info.append({'title': 'Venue Description', 'message': 'Add a detailed description (at least 50 characters)', 'priority': 'high'})
    if not venue.main_image:
        venue_missing_info.append({'title': 'Featured Image', 'message': 'Add a main image to showcase your venue', 'priority': 'high'})
    if not services.exists():
        venue_missing_info.append({'title': 'Services', 'message': 'Add services to show what you offer', 'priority': 'high'})
    if services.count() < 2:
        venue_missing_info.append({'title': 'More Services', 'message': 'Add at least 2 services for better customer options', 'priority': 'medium'})
    if not images.exists():
        venue_missing_info.append({'title': 'Gallery Images', 'message': 'Add more images to showcase your venue', 'priority': 'medium'})
    if images.count() < 3:
        venue_missing_info.append({'title': 'More Images', 'message': 'Add at least 3 images for better presentation', 'priority': 'medium'})
    if not venue.phone:
        venue_missing_info.append({'title': 'Phone Number', 'message': 'Add contact information for customers', 'priority': 'medium'})
    if not venue.email:
        venue_missing_info.append({'title': 'Email Address', 'message': 'Add email for customer inquiries', 'priority': 'medium'})
    if not opening_hours.exists():
        venue_missing_info.append({'title': 'Operating Hours', 'message': 'Add your business hours', 'priority': 'medium'})
    if not faqs.exists():
        venue_missing_info.append({'title': 'FAQs', 'message': 'Add frequently asked questions', 'priority': 'low'})
    if not amenities.exists():
        venue_missing_info.append({'title': 'Amenities', 'message': 'Add amenities you offer', 'priority': 'low'})
    if not venue.website_url:
        venue_missing_info.append({'title': 'Website', 'message': 'Add your website URL', 'priority': 'low'})

    # Add booking count for impact calculations
    if hasattr(venue, 'bookings'):
        venue._booking_count = venue.bookings.filter(
            status__in=['confirmed', 'pending']
        ).count()

    context = {
        'venue': venue,
        'services': services,
        'opening_hours': opening_hours,
        'faqs': faqs,
        'images': images,
        'primary_image': primary_image,
        'gallery_images': gallery_images,
        'amenities': amenities,
        'price_range': price_range,
        'can_add_faq': can_add_faq,
        'max_faqs': MAX_FAQS_PER_VENUE,
        'is_ready_for_approval': is_ready_for_approval,
        # Enhanced approval workflow data
        'approval_timeline': approval_timeline,
        'impact_preview': impact_preview,
        'approval_guidance': approval_guidance,
        'approval_progress': approval_progress,
        'can_submit_for_approval': can_submit_for_approval,
        # Completeness data
        'venue_completeness_score': venue_completeness_score,
        'profile_completeness_score': profile_completeness_score,
        'venue_missing_info': venue_missing_info,
        'profile_missing_info': profile_missing_info,
    }
    return render(request, 'venues_app/provider/venue_detail.html', context)


@login_required
def change_venue_status(request, venue_id):
    """Allow service providers to change their venue status (draft/pending approval)."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can change venue status.')
        return redirect('home')

    venue = get_object_or_404(
        Venue,
        id=venue_id,
        service_provider=request.user.service_provider_profile
    )

    if request.method == 'POST':
        action = request.POST.get('action')

        if action == 'submit_for_approval':
            # Use the new validation system
            success, message, missing_requirements = venue.submit_for_approval()

            if success:
                messages.success(request, message)
            else:
                messages.error(request, message)

                # Show missing requirements if any
                if missing_requirements:
                    requirement_messages = []
                    for req in missing_requirements[:3]:  # Show first 3 requirements
                        requirement_messages.append(f"• {req['requirement']}: {req['description']}")

                    if len(missing_requirements) > 3:
                        requirement_messages.append(f"• And {len(missing_requirements) - 3} more requirements...")

                    messages.info(
                        request,
                        f"Missing requirements:\n" + "\n".join(requirement_messages)
                    )

        elif action == 'save_as_draft':
            venue.approval_status = Venue.DRAFT
            venue.save(update_fields=['approval_status'])
            messages.success(
                request,
                'Your venue has been saved as a draft. You can continue editing and submit for approval when ready.'
            )
        else:
            messages.error(request, 'Invalid action.')

    return redirect('venues_app:provider_venue_detail', venue_id=venue.id)


def venue_progress(request):
    """Display venue creation/setup progress for service providers."""
    return redirect('venues_app:provider_venues')


def trigger_auto_approval_check(request):
    """Trigger auto-approval check for a venue."""
    return redirect('venues_app:provider_venues')


def bulk_service_actions(request):
    """Handle bulk actions on services."""
    return redirect('venues_app:manage_services')


class ServiceCreateView(CreateView):
    """Create view for services."""
    model = Service
    fields = ['service_title', 'short_description', 'price_min']
    template_name = 'venues_app/service_form.html'


class ServiceUpdateView(UpdateView):
    """Update view for services."""
    model = Service
    fields = ['service_title', 'short_description', 'price_min']
    template_name = 'venues_app/service_form.html'


class ServiceDeleteView(DeleteView):
    """Delete view for services."""
    model = Service
    template_name = 'venues_app/service_delete.html'


def service_create(request):
    """Create a new service."""
    if not request.user.is_authenticated:
        return redirect('accounts_app:customer_login')
    
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before adding services.')
        return redirect('venues_app:venue_create')

    # Check if user has reached maximum services limit
    if venue.services.count() >= MAX_SERVICES_PER_VENUE:
        messages.error(request, f'You have reached the maximum limit of {MAX_SERVICES_PER_VENUE} services per venue.')
        return redirect('venues_app:manage_services')

    if request.method == 'POST':
        form = ServiceForm(request.POST, venue=venue)
        if form.is_valid():
            service = form.save(commit=False)
            service.venue = venue
            service.save()
            messages.success(request, f'Service "{service.service_title}" has been created successfully!')
            return redirect('venues_app:manage_services')
        else:
            # Debug: Print form errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = ServiceForm(venue=venue)

    context = {
        'form': form,
        'venue': venue,
        'max_services': MAX_SERVICES_PER_VENUE,
        'current_services_count': venue.services.count(),
    }
    return render(request, 'venues_app/service_create.html', context)


def service_edit(request, pk):
    """Edit an existing service."""
    if not request.user.is_authenticated:
        return redirect('accounts_app:customer_login')
    
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
        service = get_object_or_404(Service, id=pk, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        form = ServiceForm(request.POST, instance=service, venue=venue)
        if form.is_valid():
            service = form.save()
            messages.success(request, f'Service "{service.service_title}" has been updated successfully!')
            return redirect('venues_app:manage_services')
        else:
            # Debug: Print form errors
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')
    else:
        form = ServiceForm(instance=service, venue=venue)

    context = {
        'form': form,
        'service': service,
        'venue': venue,
        'object': service,  # For template compatibility
    }
    return render(request, 'venues_app/service_edit.html', context)


def service_delete(request, pk):
    """Delete an existing service."""
    if not request.user.is_authenticated:
        return redirect('accounts_app:customer_login')
    
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
        service = get_object_or_404(Service, id=pk, venue=venue)
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        service_title = service.service_title
        service.delete()
        messages.success(request, f'Service "{service_title}" has been deleted successfully!')
        return redirect('venues_app:manage_services')

    context = {
        'service': service,
        'venue': venue,
        'object': service,  # For template compatibility
    }
    return render(request, 'venues_app/service_delete.html', context)


@login_required
def manage_venue_images(request):
    """Manage venue images."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('/')

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first before managing images.')
        return redirect('venues_app:venue_create')

    if request.method == 'POST':
        # Handle image upload via regular POST (non-AJAX)
        max_images = 5
        current_count = venue.images.filter(is_active=True).count()
        
        if current_count >= max_images:
            messages.error(request, f'Maximum {max_images} images allowed per venue.')
            return redirect('venues_app:manage_venue_images')

        form = VenueImageForm(request.POST, request.FILES)
        if form.is_valid():
            venue_image = form.save(commit=False)
            venue_image.venue = venue
            
            # Set as primary if it's the first image
            if current_count == 0:
                venue_image.is_primary = True
                
            venue_image.save()
            messages.success(request, 'Image uploaded successfully!')
            return redirect('venues_app:manage_venue_images')
        else:
            for field, errors in form.errors.items():
                for error in errors:
                    messages.error(request, f'{field}: {error}')

    images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    form = VenueImageForm()
    max_images = 5
    can_add_image = images.count() < max_images

    context = {
        'venue': venue,
        'images': images,
        'form': form,
        'max_images': max_images,
        'can_add_image': can_add_image,
    }
    return render(request, 'venues_app/provider/manage_images.html', context)


@login_required
def upload_venue_image(request):
    """Upload venue image via AJAX."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can upload images'})

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'You need to create a venue first'})

    # Check if venue has reached max images limit
    max_images = 5
    current_count = venue.images.filter(is_active=True).count()
    if current_count >= max_images:
        return JsonResponse({
            'success': False, 
            'error': f'Maximum {max_images} images allowed per venue'
        })

    form = VenueImageForm(request.POST, request.FILES)
    if form.is_valid():
        venue_image = form.save(commit=False)
        venue_image.venue = venue
        
        # Set as primary if it's the first image
        if current_count == 0:
            venue_image.is_primary = True
            
        venue_image.save()
        
        return JsonResponse({
            'success': True,
            'image_id': venue_image.id,
            'image_url': venue_image.image.url,
            'message': 'Image uploaded successfully'
        })
    else:
        return JsonResponse({
            'success': False,
            'errors': form.errors
        })


@login_required 
def set_primary_image(request, image_id):
    """Set primary venue image."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can modify images'})

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Venue not found'})

    try:
        image = VenueImage.objects.get(id=image_id, is_active=True)
    except VenueImage.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Image not found'})

    # Check if the image belongs to the user's venue
    if image.venue != venue:
        return JsonResponse({'success': False, 'error': 'You can only modify images from your own venue'})

    # Set this image as primary (the model save method handles unsetting others)
    image.is_primary = True
    image.save()

    return JsonResponse({
        'success': True,
        'message': 'Primary image updated successfully'
    })


@login_required
def delete_venue_image(request, image_id):
    """Delete venue image."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can delete images'})

    try:
        venue = request.user.service_provider_profile.venue
        image = get_object_or_404(VenueImage, id=image_id, venue=venue, is_active=True)
    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Venue not found'})

    was_primary = image.is_primary
    image.delete()

    current_count = venue.images.filter(is_active=True).count()

    return JsonResponse({
        'success': True,
        'message': 'Image deleted successfully',
        'was_primary': was_primary,
        'current_count': current_count,
        'max_images': 5
    })


@login_required
def reorder_venue_images(request):
    """Reorder venue images."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can reorder images'})

    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        return JsonResponse({'success': False, 'error': 'Venue not found'})

    try:
        import json
        data = json.loads(request.body)
        image_orders = data.get('image_orders', [])
        
        for item in image_orders:
            image_id = item.get('id')
            new_order = item.get('order')
            
            try:
                image = VenueImage.objects.get(id=image_id, venue=venue, is_active=True)
                image.order = new_order
                image.save(update_fields=['order'])
            except VenueImage.DoesNotExist:
                continue
                
        return JsonResponse({
            'success': True,
            'message': 'Images reordered successfully'
        })
        
    except (json.JSONDecodeError, KeyError):
        return JsonResponse({'success': False, 'error': 'Invalid request data'})


def validate_venue_image_preview(request):
    """Validate venue image preview."""
    return JsonResponse({'status': 'ok'})


def reorder_venue_image(request):
    """Reorder venue image."""
    return redirect('venues_app:provider_venues')


def undo_image_action(request):
    """Undo image action."""
    return redirect('venues_app:provider_venues')


def manage_holiday_schedules(request):
    """Manage holiday schedules."""
    return redirect('venues_app:provider_venues')


def delete_holiday_schedule(request):
    """Delete holiday schedule."""
    return redirect('venues_app:provider_venues')


def validate_field_ajax(request):
    """Validate field via AJAX."""
    return JsonResponse({'status': 'ok'})


def auto_save_progress(request):
    """Auto save progress."""
    return JsonResponse({'status': 'ok'})


def sync_contact_info(request):
    """Sync contact info."""
    return redirect('venues_app:provider_venues')


def send_email_verification(request):
    """Send email verification."""
    return redirect('venues_app:provider_venues')


def verify_venue_email(request):
    """Verify venue email."""
    return redirect('venues_app:provider_venues')


def manage_venue_visibility(request):
    """Manage venue visibility."""
    return redirect('venues_app:provider_venues')


@login_required
def venue_preview(request):
    """Preview venue as it appears to customers."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can access this page.')
        return redirect('home')

    # Get the provider's venue
    venue = Venue.objects.filter(
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    ).prefetch_related('services', 'faqs', 'images', 'categories', 'amenities').first()

    if not venue:
        messages.error(request, 'No venue found. Please create a venue first.')
        return redirect('venues_app:venue_create')

    services = venue.services.all().order_by('service_title')
    opening_hours = venue.operating_hours_set.all().order_by('day')
    faqs = venue.faqs.all().order_by('order')
    images = venue.images.filter(is_active=True).order_by('-is_primary', 'order')
    primary_image = images.filter(is_primary=True).first()
    gallery_images = images.filter(is_primary=False)
    amenities = venue.amenities.filter(is_active=True).order_by('amenity_type')

    # Calculate price range from services
    price_range = None
    if services.exists():
        prices = [service.price_min for service in services if service.price_min]
        if prices:
            min_price = min(prices)
            max_prices = [service.price_max or service.price_min for service in services if service.price_min]
            max_price = max(max_prices) if max_prices else min_price
            price_range = f"${min_price}" if min_price == max_price else f"${min_price} - ${max_price}"

    # Calculate completeness score
    total_items = 0
    completed_items = 0
    
    # Basic info (venue name, description, main image)
    total_items += 3
    if venue.venue_name:
        completed_items += 1
    if venue.short_description:
        completed_items += 1
    if venue.main_image:
        completed_items += 1
    
    # Services
    total_items += 1
    if services.exists():
        completed_items += 1
    
    # Images
    total_items += 1
    if images.exists():
        completed_items += 1
    
    # Contact info
    total_items += 2
    if venue.phone:
        completed_items += 1
    if venue.email:
        completed_items += 1
    
    # Operating hours
    total_items += 1
    if opening_hours.exists():
        completed_items += 1
    
    # FAQs
    total_items += 1
    if faqs.exists():
        completed_items += 1
    
    # Amenities
    total_items += 1
    if amenities.exists():
        completed_items += 1

    completeness_percentage = int((completed_items / total_items) * 100) if total_items > 0 else 0

    # Get missing items for guidance
    missing_info = []
    if not venue.venue_name:
        missing_info.append({'title': 'Venue Name', 'message': 'Add a name for your venue', 'priority': 'high'})
    if not venue.short_description:
        missing_info.append({'title': 'Venue Description', 'message': 'Add a description to tell customers about your venue', 'priority': 'high'})
    if not venue.main_image:
        missing_info.append({'title': 'Featured Image', 'message': 'Add a main image to showcase your venue', 'priority': 'high'})
    if not services.exists():
        missing_info.append({'title': 'Services', 'message': 'Add services to show what you offer', 'priority': 'high'})
    if not images.exists():
        missing_info.append({'title': 'Gallery Images', 'message': 'Add more images to showcase your venue', 'priority': 'medium'})
    if not venue.phone:
        missing_info.append({'title': 'Phone Number', 'message': 'Add contact information for customers', 'priority': 'medium'})
    if not venue.email:
        missing_info.append({'title': 'Email Address', 'message': 'Add email for customer inquiries', 'priority': 'medium'})
    if not opening_hours.exists():
        missing_info.append({'title': 'Operating Hours', 'message': 'Add your business hours', 'priority': 'medium'})
    if not faqs.exists():
        missing_info.append({'title': 'FAQs', 'message': 'Add frequently asked questions', 'priority': 'low'})
    if not amenities.exists():
        missing_info.append({'title': 'Amenities', 'message': 'Add amenities you offer', 'priority': 'low'})

    # Calculate freshness info (simplified for preview)
    freshness_info = {
        'description': {'is_recent': True, 'is_stale': False},
        'hours': {'is_recent': True, 'is_stale': False},
        'contact': {'is_recent': True, 'is_stale': False},
        'amenities': {'is_recent': True, 'is_stale': False},
    }

    context = {
        'venue': venue,
        'services': services,
        'opening_hours': opening_hours,
        'faqs': faqs,
        'images': images,
        'primary_image': primary_image,
        'gallery_images': gallery_images,
        'amenities': amenities,
        'price_range': price_range,
        'completeness_score': completeness_percentage,
        'missing_info': missing_info,
        'freshness_info': freshness_info,
        'team_members': [],  # Empty for now, can be populated later if needed
        'is_preview_mode': True,
    }
    
    return render(request, 'venues_app/venue_preview.html', context)


def update_information_freshness(request):
    """Update information freshness."""
    return redirect('venues_app:provider_venues')


@login_required
def edit_venue_basic_information(request, venue_id):
    """Allow service providers to edit their venue's basic information."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit venue information.')
        return redirect('home')

    venue = get_object_or_404(
        Venue.objects.select_related('service_provider'),
        id=venue_id,
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    )

    if request.method == 'POST':
        form = VenueBasicInformationEditForm(request.POST, instance=venue, user=request.user)
        if form.is_valid():
            # Save the venue with updated information
            updated_venue = form.save(commit=False)
            updated_venue.updated_at = timezone.now()
            updated_venue.save()

            # Save many-to-many relationships (categories)
            form.save_m2m()

            # Update completion score
            updated_venue.update_completeness_score()

            messages.success(request, 'Venue basic information updated successfully.')
            return redirect('venues_app:provider_venue_detail', venue_id=venue.id)
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = VenueBasicInformationEditForm(instance=venue, user=request.user)

    context = {
        'venue': venue,
        'form': form,
        'page_title': 'Edit Basic Information',
        'breadcrumbs': [
            {'name': 'My Venue', 'url': reverse('venues_app:provider_venue_detail', kwargs={'venue_id': venue.id})},
            {'name': 'Edit Basic Information', 'url': None}
        ]
    }
    return render(request, 'venues_app/provider/edit_basic_information.html', context)


@login_required
def edit_venue_location(request, venue_id):
    """Allow service providers to edit their venue's location details."""
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'Only service providers can edit venue location.')
        return redirect('home')

    venue = get_object_or_404(
        Venue.objects.select_related('service_provider'),
        id=venue_id,
        service_provider=request.user.service_provider_profile,
        is_deleted=False
    )

    if request.method == 'POST':
        form = VenueLocationEditForm(request.POST, instance=venue, user=request.user)
        if form.is_valid():
            # Save the venue with updated location information
            updated_venue = form.save(commit=False)
            updated_venue.updated_at = timezone.now()

            # Update full address field
            updated_venue.full_address = f"{updated_venue.street_number} {updated_venue.street_name}, {updated_venue.city}, {updated_venue.county}, {updated_venue.state}"

            updated_venue.save()

            # Update completion score
            updated_venue.update_completeness_score()

            messages.success(request, 'Venue location updated successfully.')
            return redirect('venues_app:provider_venue_detail', venue_id=venue.id)
        else:
            messages.error(request, 'Please correct the errors below.')
    else:
        form = VenueLocationEditForm(instance=venue, user=request.user)

    context = {
        'venue': venue,
        'form': form,
        'page_title': 'Edit Location',
        'breadcrumbs': [
            {'name': 'My Venue', 'url': reverse('venues_app:provider_venue_detail', kwargs={'venue_id': venue.id})},
            {'name': 'Edit Location', 'url': None}
        ]
    }
    return render(request, 'venues_app/provider/edit_location.html', context)


@login_required
def upload_wizard_image(request):
    """Upload image during venue creation wizard via AJAX."""
    if request.method != 'POST':
        return JsonResponse({'success': False, 'error': 'Only POST method allowed'})

    if not hasattr(request.user, 'service_provider_profile'):
        return JsonResponse({'success': False, 'error': 'Only service providers can upload images'})

    # Check if image file is provided
    if 'image' not in request.FILES:
        return JsonResponse({'success': False, 'error': 'No image file provided'})

    image_file = request.FILES['image']
    
    # Validate file type
    allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
    if image_file.content_type not in allowed_types:
        return JsonResponse({
            'success': False, 
            'error': 'Invalid file type. Please upload JPEG, PNG, or WebP images only.'
        })
    
    # Validate file size (max 5MB)
    max_size = 5 * 1024 * 1024  # 5MB in bytes
    if image_file.size > max_size:
        return JsonResponse({
            'success': False,
            'error': 'File too large. Maximum size is 5MB.'
        })

    try:
        from ..forms.venue import VenueImageForm
        from django.core.files.storage import default_storage
        import uuid
        import os
        
        # Generate a unique filename for temporary storage
        file_extension = os.path.splitext(image_file.name)[1]
        temp_filename = f"temp_wizard_{request.user.id}_{uuid.uuid4().hex[:8]}{file_extension}"
        
        # Save to temporary location
        temp_path = default_storage.save(
            f"temp/venue_wizard/{temp_filename}",
            image_file
        )
        
        # Get the URL for the uploaded file
        file_url = default_storage.url(temp_path)
        
        return JsonResponse({
            'success': True,
            'image_url': file_url,
            'image_path': temp_path,
            'filename': image_file.name,
            'message': 'Image uploaded successfully'
        })
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Error uploading wizard image: {e}")
        
        return JsonResponse({
            'success': False,
            'error': 'Failed to upload image. Please try again.'
        })


class VenueEditWizardView:
    """Multi-step wizard for editing existing venues with progress tracking"""
    
    STEP_CHOICES = [
        ('basic', _('Basic Information')),
        ('location', _('Location & Contact')),
        ('services', _('Services & Pricing')),
        ('gallery', _('Gallery & Images')),
        ('details', _('Details & Policies')),
    ]
    
    def __init__(self, request, user, venue):
        self.request = request
        self.user = user
        self.venue = venue
        self.session_key = f'venue_edit_wizard_{venue.id}'
    
    def get_form_class(self, step):
        """Get the appropriate form class for the current step"""
        from ..forms.venue import (
            VenueEditBasicInfoForm, VenueEditLocationForm, VenueEditServicesForm,
            VenueEditGalleryForm, VenueEditDetailsForm
        )
        
        form_classes = {
            'basic': VenueEditBasicInfoForm,
            'location': VenueEditLocationForm,
            'services': VenueEditServicesForm,
            'gallery': VenueEditGalleryForm,
            'details': VenueEditDetailsForm,
        }
        return form_classes.get(step, VenueEditBasicInfoForm)
    
    def get_form(self, step, data=None, files=None):
        """Get form instance for the current step"""
        form_class = self.get_form_class(step)
        
        # If no data provided, try to load from session
        if data is None:
            progress_data = self.get_progress()
            if step in progress_data:
                data = progress_data[step]
        
        return form_class(
            data=data,
            files=files,
            current_step=step,
            venue=self.venue
        )
    
    def get_progress(self):
        """Get progress data for the wizard"""
        try:
            progress_data = self.request.session.get(self.session_key, {})
            return progress_data
        except Exception as e:
            # If session data is corrupted, clear it and start fresh
            logging.warning(f"Failed to load progress data: {e}")
            self.clear_progress()
            return {}
    
    def clear_progress(self):
        """Clear progress data from session"""
        if self.session_key in self.request.session:
            del self.request.session[self.session_key]
            self.request.session.modified = True
    
    def save_progress(self, step, data):
        """Save progress data for a specific step"""
        progress_data = self.get_progress()
        # Convert data to JSON-serializable format
        serializable_data = self._make_serializable(data)
        progress_data[step] = serializable_data
        progress_data['last_updated'] = timezone.now().isoformat()
        self.request.session[self.session_key] = progress_data
        self.request.session.modified = True
    
    def _make_serializable(self, data):
        """Convert data to JSON-serializable format"""
        try:
            if isinstance(data, dict):
                return {key: self._make_serializable(value) for key, value in data.items()}
            elif isinstance(data, list):
                return [self._make_serializable(item) for item in data]
            elif hasattr(data, 'values_list'):  # QuerySet
                return list(data.values_list('id', flat=True))
            elif hasattr(data, 'id'):  # Model instance
                return data.id
            elif hasattr(data, 'isoformat'):  # datetime objects
                return data.isoformat()
            elif isinstance(data, decimal.Decimal):
                return float(data)
            else:
                return data
        except Exception as e:
            logging.warning(f"Failed to serialize data: {e}")
            return None
    
    def get_completed_steps(self):
        """Get list of completed steps"""
        progress_data = self.get_progress()
        return [step for step, data in progress_data.items() if step != 'last_updated' and data]
    
    def _calculate_progress(self, progress_data):
        """Calculate progress percentage"""
        total_steps = len(self.STEP_CHOICES)
        completed_steps = len([step for step, data in progress_data.items() 
                             if step != 'last_updated' and data])
        return int((completed_steps / total_steps) * 100)
    
    def get_next_step(self, current_step):
        """Get the next step in the wizard"""
        step_order = [step[0] for step in self.STEP_CHOICES]
        try:
            current_index = step_order.index(current_step)
            if current_index < len(step_order) - 1:
                return step_order[current_index + 1]
        except ValueError:
            pass
        return None
    
    def get_previous_step(self, current_step):
        """Get the previous step in the wizard"""
        step_order = [step[0] for step in self.STEP_CHOICES]
        try:
            current_index = step_order.index(current_step)
            if current_index > 0:
                return step_order[current_index - 1]
        except ValueError:
            pass
        return None
    
    def is_step_complete(self, step):
        """Check if a step is complete"""
        progress_data = self.get_progress()
        return step in progress_data and progress_data[step]
    
    def can_access_step(self, step):
        """Check if user can access a specific step"""
        step_order = [step[0] for step in self.STEP_CHOICES]
        try:
            step_index = step_order.index(step)
            # Allow access to current step and all previous steps
            for i in range(step_index):
                if not self.is_step_complete(step_order[i]):
                    return False
            return True
        except ValueError:
            return False
    
    def handle_complex_data(self, step, form_data):
        """Handle complex data like JSON fields"""
        if step == 'services':
            # Handle services data
            if 'services' in form_data:
                try:
                    services_data = json.loads(form_data['services'])
                    form_data['services'] = services_data
                except (json.JSONDecodeError, TypeError):
                    form_data['services'] = []
        
        elif step == 'gallery':
            # Handle gallery data
            if 'images' in form_data:
                try:
                    images_data = json.loads(form_data['images'])
                    form_data['images'] = images_data
                except (json.JSONDecodeError, TypeError):
                    form_data['images'] = []
        
        elif step == 'details':
            # Handle operating hours and FAQs
            if 'operating_hours' in form_data:
                try:
                    operating_hours_data = json.loads(form_data['operating_hours'])
                    form_data['operating_hours'] = operating_hours_data
                except (json.JSONDecodeError, TypeError):
                    form_data['operating_hours'] = []
            
            if 'faqs' in form_data:
                try:
                    faqs_data = json.loads(form_data['faqs'])
                    form_data['faqs'] = faqs_data
                except (json.JSONDecodeError, TypeError):
                    form_data['faqs'] = []
        
        return form_data
    
    def get_step_requirements(self, step):
        """Get requirements for each step"""
        requirements = {
            'basic': {
                'required_fields': ['venue_name', 'short_description', 'categories'],
                'description': 'Update basic venue information',
                'details': [
                    'Enter a unique venue name (3-100 characters)',
                    'Write a compelling description (50-500 characters)', 
                    'Select 1-3 relevant categories'
                ]
            },
            'location': {
                'required_fields': ['street_address', 'city', 'state', 'zip_code'],
                'description': 'Update location and contact details',
                'details': [
                    'Enter complete street address',
                    'Verify city, state, and ZIP code',
                    'Add business phone number',
                    'Optional: Add email and website'
                ]
            },
            'services': {
                'required_fields': ['services'],
                'description': 'Update your services with pricing',
                'details': [
                    'Add at least 1 service',
                    'Include clear service descriptions',
                    'Set appropriate pricing'
                ]
            },
            'gallery': {
                'required_fields': ['images'],
                'description': 'Update venue photos',
                'details': [
                    'Upload at least 1 main image',
                    'Add 2-5 gallery images (recommended)',
                    'Use high-quality, well-lit photos'
                ]
            },
            'details': {
                'required_fields': ['operating_hours', 'amenities'],
                'description': 'Update operating hours, amenities, and policies',
                'details': [
                    'Set your operating hours',
                    'Select available amenities',
                    'Add FAQs and policies (optional)'
                ]
            },
        }
        return requirements.get(step, {})


@login_required
def venue_edit_wizard_view(request, step='basic'):
    """Multi-step venue editing wizard with progress saving."""
    # Ensure user has service provider profile
    if not hasattr(request.user, 'service_provider_profile'):
        messages.error(request, 'You must have a service provider profile to edit a venue.')
        return redirect('accounts_app:service_provider_profile')

    # Get the user's venue
    try:
        venue = request.user.service_provider_profile.venue
    except Venue.DoesNotExist:
        messages.error(request, 'You need to create a venue first.')
        return redirect('venues_app:venue_create')

    # Initialize wizard
    wizard = VenueEditWizardView(request, request.user, venue)
    
    # Check if user can access the requested step
    if not wizard.can_access_step(step):
        # Redirect to the first incomplete step
        for wizard_step, _ in wizard.STEP_CHOICES:
            if not wizard.is_step_complete(wizard_step):
                return redirect('venues_app:venue_edit_wizard', step=wizard_step)
        # If all steps are complete, go to the first step
        return redirect('venues_app:venue_edit_wizard', step='basic')
    
    # Handle AJAX requests for progress saving
    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
        return handle_edit_wizard_ajax(request, wizard, step)
    
    # Handle form submission
    if request.method == 'POST':
        form = wizard.get_form(step, data=request.POST, files=request.FILES)
        
        if form.is_valid():
            # Save progress for this step
            cleaned_data = form.cleaned_data
            wizard.save_progress(step, cleaned_data)
            
            # Check if this is the final step
            if step == 'details':
                # Process the complete venue update
                success = update_venue_from_wizard(request, wizard, form)
                if success:
                    return redirect('venues_app:provider_venue_detail', venue_id=venue.id)
            else:
                # Move to next step
                next_step = wizard.get_next_step(step)
                if next_step:
                    return redirect('venues_app:venue_edit_wizard', step=next_step)
                else:
                    # If no next step, go to details (final step)
                    return redirect('venues_app:venue_edit_wizard', step='details')
        else:
            # Form is invalid, show errors
            messages.error(request, 'Please correct the errors below.')
    else:
        # GET request - show form
        form = wizard.get_form(step)
    
    # Get progress information
    progress_data = wizard.get_progress()
    completed_steps = wizard.get_completed_steps()
    step_choices = wizard.STEP_CHOICES
    step_order = [step[0] for step in step_choices]
    
    context = {
        'form': form,
        'venue': venue,
        'current_step': step,
        'step_choices': step_choices,
        'current_step_title': dict(step_choices).get(step, step.title()),
        'progress_data': progress_data,
        'completed_steps': completed_steps,
        'progress_percentage': wizard._calculate_progress(progress_data),
        'next_step': wizard.get_next_step(step),
        'previous_step': wizard.get_previous_step(step),
        'is_first_step': step == 'basic',
        'is_final_step': step == 'details',
        'step_number': step_order.index(step) + 1 if step in step_order else 1,
        'total_steps': 5,
        'step_requirements': wizard.get_step_requirements(step),
    }
    
    return render(request, 'venues_app/venue_edit_wizard.html', context)


def handle_edit_wizard_ajax(request, wizard, step):
    """Handle AJAX requests for the edit wizard"""
    if request.method == 'POST':
        try:
            data = json.loads(request.body)
            action = data.get('action')
            
            if action == 'save_progress':
                form_data = data.get('form_data', {})
                # Handle complex data
                form_data = wizard.handle_complex_data(step, form_data)
                wizard.save_progress(step, form_data)
                
                return JsonResponse({
                    'success': True,
                    'message': 'Progress saved successfully',
                    'progress_percentage': wizard._calculate_progress(wizard.get_progress())
                })
            
            elif action == 'get_progress':
                return JsonResponse({
                    'success': True,
                    'progress_data': wizard.get_progress(),
                    'completed_steps': wizard.get_completed_steps(),
                    'progress_percentage': wizard._calculate_progress(wizard.get_progress())
                })
            
        except json.JSONDecodeError:
            return JsonResponse({'success': False, 'message': 'Invalid JSON data'})
        except Exception as e:
            return JsonResponse({'success': False, 'message': str(e)})
    
    return JsonResponse({'success': False, 'message': 'Invalid request method'})


def update_venue_from_wizard(request, wizard, form):
    """Update venue from wizard data"""
    try:
        # Get all progress data
        progress_data = wizard.get_progress()
        if not progress_data:
            messages.error(request, 'No progress data found. Please start the venue editing process again.')
            return False

        venue = wizard.venue
        
        # Update venue with data from each step
        if 'basic' in progress_data:
            basic_data = progress_data['basic']
            venue.venue_name = basic_data.get('venue_name', venue.venue_name)
            venue.short_description = basic_data.get('short_description', venue.short_description)
            if 'categories' in basic_data:
                # Convert category IDs back to Category objects
                from ..models import Category
                category_ids = basic_data['categories']
                if isinstance(category_ids, list):
                    categories = Category.objects.filter(id__in=category_ids)
                    venue.categories.set(categories)
        
        if 'location' in progress_data:
            location_data = progress_data['location']
            venue.state = location_data.get('state', venue.state)
            venue.county = location_data.get('county', venue.county)
            venue.city = location_data.get('city', venue.city)
            venue.street_number = location_data.get('street_number', venue.street_number)
            venue.street_name = location_data.get('street_name', venue.street_name)
            # Note: zip_code field is not available in the main Venue model
            # It's only available during venue creation via VenueCreationDraft
            venue.latitude = location_data.get('latitude', venue.latitude)
            venue.longitude = location_data.get('longitude', venue.longitude)
            venue.phone = location_data.get('phone', venue.phone)
            venue.email = location_data.get('email', venue.email)
            venue.website_url = location_data.get('website_url', venue.website_url)
            venue.instagram_url = location_data.get('instagram_url', venue.instagram_url)
            venue.facebook_url = location_data.get('facebook_url', venue.facebook_url)
            venue.twitter_url = location_data.get('twitter_url', venue.twitter_url)
            venue.linkedin_url = location_data.get('linkedin_url', venue.linkedin_url)
        
        if 'details' in progress_data:
            details_data = progress_data['details']
            # Note: Policy fields are not available in the main Venue model
            # They are only available during venue creation via VenueCreationDraft
            # These fields will be ignored for existing venues
            
            # Handle amenities
            if 'amenities' in details_data:
                # Clear existing amenities - use all().delete() for RelatedManager
                venue.amenities.all().delete()
                amenities_data = details_data['amenities']
                if isinstance(amenities_data, list):
                    for amenity_type in amenities_data:
                        venue.amenities.create(amenity_type=amenity_type)
            
            # Handle operating hours
            if 'operating_hours' in details_data:
                # Clear existing operating hours
                venue.operating_hours_set.all().delete()
                
                # Create new operating hours from the data
                from ..models import OperatingHours
                from datetime import time
                
                for hours_data in details_data['operating_hours']:
                    if isinstance(hours_data, dict):
                        # Parse time strings back to time objects
                        opening_time = None
                        closing_time = None
                        
                        if hours_data.get('open'):
                            try:
                                opening_time = time.fromisoformat(hours_data['open'])
                            except ValueError:
                                pass
                        
                        if hours_data.get('close'):
                            try:
                                closing_time = time.fromisoformat(hours_data['close'])
                            except ValueError:
                                pass
                        
                        OperatingHours.objects.create(
                            venue=venue,
                            day=hours_data.get('day', 0),
                            opening=opening_time,
                            closing=closing_time,
                            is_closed=hours_data.get('is_closed', False),
                            is_24_hours=hours_data.get('is_24_hours', False),
                            is_overnight=hours_data.get('is_overnight', False),
                        )
            
            # Handle FAQs
            if 'faqs' in details_data:
                # Update existing FAQs or create new ones
                existing_faq_ids = []
                for faq_data in details_data['faqs']:
                    if 'id' in faq_data:
                        # Update existing FAQ
                        try:
                            faq = venue.faqs.get(id=faq_data['id'])
                            faq.question = faq_data['question']
                            faq.answer = faq_data['answer']
                            faq.order = faq_data.get('order', 0)
                            faq.save()
                            existing_faq_ids.append(faq.id)
                        except VenueFAQ.DoesNotExist:
                            pass
                    else:
                        # Create new FAQ
                        faq = venue.faqs.create(
                            question=faq_data['question'],
                            answer=faq_data['answer'],
                            order=faq_data.get('order', 0)
                        )
                        existing_faq_ids.append(faq.id)
                
                # Remove FAQs that are no longer in the list
                venue.faqs.exclude(id__in=existing_faq_ids).delete()
            
            # Set approval status based on venue_status selection
            venue_status = details_data.get('venue_status', 'draft')
            if venue_status == 'draft':
                venue.approval_status = Venue.DRAFT
            else:
                venue.approval_status = Venue.PENDING
        
        # Save the venue
        venue.save()
        
        # Clear the wizard session data
        if wizard.session_key in request.session:
            del request.session[wizard.session_key]
            request.session.modified = True
        
        messages.success(
            request,
            '✅ Your venue has been updated successfully! '
            'Changes will be visible to customers immediately.'
        )
        
        return True
        
    except Exception as e:
        messages.error(request, f'Error updating venue: {str(e)}')
        return False


